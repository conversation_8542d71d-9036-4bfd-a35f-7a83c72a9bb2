<template>
  <div class="app-container">
    <!-- 上传区域 -->
    <el-card class="upload-card" shadow="never">
      <div class="upload-area">
        <el-upload
            class="upload-demo"
            drag
            action="#"
            :auto-upload="false"
            :on-change="handleUpload"
            :show-file-list="false"
            accept=".doc,.docx"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            拖拽试卷文件到此处或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              支持上传 .doc 或 .docx 格式的试卷文件
            </div>
          </template>
        </el-upload>
      </div>
    </el-card>

    <!-- 历史记录区域 -->
    <el-card class="history-card" shadow="never">
      <div slot="header" class="history-header">
        <span>历史解析记录</span>
      </div>

      <!-- 历史记录列表 -->
      <div class="history-list">
        <div
            v-for="item in filteredHistory"
            :key="item.title"
            class="history-item"
            @click="handleHistoryClick(item)"
        >
          <div class="file-name">{{ formatTitle(item.title) }}</div>
          <div class="right-group">
            <span class="upload-time">{{ item.uploadTime }}</span>
            <span class="status-tag" :class="getStatusClass(item.status)">
              {{ getStatusText(item.status) }}
            </span>
            <el-icon class="arrow-icon"><arrow-right /></el-icon>
          </div>
        </div>
        <div class="empty-state" v-if="filteredHistory.length === 0">
          <el-empty description="暂无上传记录" />
        </div>
      </div>
    </el-card>

    <div v-loading="loading" element-loading-text="试卷解析中，请耐心等待..." class="content-container">
      <!-- 试卷信息区域 -->
      <el-card v-if="examInfo.title" shadow="never" class="info-card">
        <div class="card-header">
          <span>试卷信息</span>
        </div>
        <el-form label-width="100px" class="info-form">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="试卷名称">
                <el-input v-model="examInfo.title" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="试卷类型">
                <el-select v-model="examInfo.type" clearable placeholder="卷型选择"
                           style="width: 100%">
                  <el-option v-for="dict in sys_qh_paper_type" :key="dict.value" :label="dict.label"
                             :value="dict.value"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="试卷年份">
                <el-date-picker
                    v-model="examInfo.year"
                    type="year"
                    value-format="YYYY"
                    placeholder="选择年份"
                    style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20" class="form-row-margin">
            <el-col :span="8">
              <el-form-item label="所属地区">
                <el-input v-model="examInfo.area" placeholder="请输入地区" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="年级">
                <el-input v-model="examInfo.grade" placeholder="请输入年级" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="学科">
                <el-input v-model="examInfo.subject" placeholder="请输入学科" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>

      <!-- 题目编辑区域 -->
      <el-card v-if="currentQuestion" shadow="never" class="question-card">
        <div class="card-header">
          <span>题目编辑</span>
          <div class="question-counter">
            第 {{ currentIndex + 1 }} 题 / 共 {{ questions.length }} 题
          </div>
        </div>

        <el-form label-width="100px" class="question-form">
          <!-- 题目图片和答案图片行 -->
          <el-row :gutter="20" class="form-row-margin">
            <el-col :span="12">
              <el-form-item label="题目图片">
                <div class="image-container">
                  <!-- 显示图片预览 -->
                  <div class="image-preview" v-if="currentQuestion.questionImage">
                    <img
                        :src="getImageUrl(currentQuestion.questionImage)"
                        :alt="`题目图片 ${currentIndex + 1}`"
                        @error="handleImageError('question')"
                    >
                  </div>
                  <div class="placeholder" v-else>
                    无图片
                  </div>

                  <!-- 上传控件 -->
                  <el-upload
                      class="image-uploader"
                      action="#"
                      :auto-upload="false"
                      :on-change="(file) => handleImageUpload(file, 'question')"
                      :show-file-list="false"
                      accept="image/jpeg,image/png,image/jpg"
                  >
                    <el-button type="primary" size="small" class="upload-btn">重新上传</el-button>
                  </el-upload>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="答案图片">
                <div class="image-container">
                  <!-- 显示图片预览 -->
                  <div class="image-preview" v-if="currentQuestion.answerImage">
                    <img
                        :src="getImageUrl(currentQuestion.answerImage)"
                        :alt="`答案图片 ${currentIndex + 1}`"
                        @error="handleImageError('answer')"
                    >
                  </div>
                  <div class="placeholder" v-else>
                    无图片
                  </div>

                  <!-- 上传控件 -->
                  <el-upload
                      class="image-uploader"
                      action="#"
                      :auto-upload="false"
                      :on-change="(file) => handleImageUpload(file, 'answer')"
                      :show-file-list="false"
                      accept="image/jpeg,image/png,image/jpg"
                  >
                    <el-button type="primary" size="small" class="upload-btn">重新上传</el-button>
                  </el-upload>
                </div>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 试题类型和试题难度行 -->
          <el-row :gutter="20" class="form-row-margin">
            <el-col :span="12">
              <el-form-item label="试题类型">
                <el-select v-model="currentQuestion.type" placeholder="题型选择"
                           style="width: 100%">
                  <el-option v-for="dict in sys_qh_questions_type" :key="dict.value" :label="dict.label"
                             :value="dict.value"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="试题难度">
                <el-select v-model="currentQuestion.difficulty" placeholder="难度选择"
                           style="width: 100%">
                  <el-option v-for="dict in sys_qh_difficulty" :key="dict.value" :label="dict.label" :value="dict.value"/>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 试题考点和试题标签行 -->
          <el-row :gutter="20" class="form-row-margin">
            <el-col :span="12">
              <el-form-item label="所属题库">
                <el-select v-model="currentQuestion.knowledgeTreeId" style="width: 100%">
                  <el-option :value="0" label="题目" />
                  <el-option :value="1" label="答案" />
                  <el-option :value="2" label="解析" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="试题考点">
                <el-select v-model="currentQuestion.knowledgeTreeId" style="width: 100%">
                  <el-option :value="0" label="题目" />
                  <el-option :value="1" label="答案" />
                  <el-option :value="2" label="解析" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 试题OCR解析内容 -->
          <el-row :gutter="20" class="form-row-margin">
            <el-col :span="12">
              <el-form-item label="试题标签">
                <el-input v-model="currentQuestion.tag" clearable placeholder="试题标签"
                          style="width: 100%"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="试题内容">
                <el-input
                    type="textarea"
                    v-model="currentQuestion.ocrKey"
                    clearable
                    placeholder="试题内容"
                    :autosize="{ minRows: 3 }"
                    style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <div class="action-buttons">
          <el-button
              type="primary"
              @click="submitQuestion"
              :disabled="currentQuestion.submitted"
          >
            <el-icon><check /></el-icon>
            {{ currentQuestion.submitted ? '已录题' : '录题' }}
          </el-button>

          <el-button-group>
            <el-button
                :disabled="currentIndex === 0"
                @click="prevQuestion"
            >
              <el-icon><arrow-left /></el-icon>
              上一题
            </el-button>
            <el-button
                :disabled="currentIndex >= questions.length - 1"
                @click="nextQuestion"
            >
              下一题
              <el-icon><arrow-right /></el-icon>
            </el-button>
          </el-button-group>

          <el-button type="success" @click="submitAll" :disabled="!allSubmitted">
            <el-icon><finished /></el-icon>
            全部提交
          </el-button>
        </div>
      </el-card>
    </div>

    <div class="stats-bar" v-if="questions.length > 0">
      <el-progress
          :percentage="submittedPercentage"
          :color="customColors"
          :show-text="false"
          style="margin-bottom: 10px;"
      />
      <div class="stats-info">
        <span class="submitted">已录题: {{ submittedCount }}</span>
        <span class="total">总题目: {{ questions.length }}</span>
        <span class="percentage">{{ submittedPercentage }}%</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import {ref, reactive, computed, getCurrentInstance, onMounted, onUnmounted} from 'vue'
import {
  UploadFilled, Check, ArrowLeft, ArrowRight, Finished, RefreshRight, Document,
  Search, ArrowRight as ArrowRightIcon
} from '@element-plus/icons-vue'
import {ElMessage, ElEmpty, ElProgress} from 'element-plus'
import {useRouter} from 'vue-router'
// 导入后端接口
import {uploadPaperFile, uploadStatus} from "@/api/qh/paperUpload.js";

const router = useRouter()

// 历史记录相关
const searchKeyword = ref('')
const historyRecords = ref([]) // 现在存储数组形式的历史记录
const refreshTimer = ref(null) // 定时器引用

// 过滤后的历史记录
const filteredHistory = computed(() => {
  if (!searchKeyword.value) return historyRecords.value

  return historyRecords.value.filter(item =>
      item.title.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 'processing':
      return '正在解析'
    case 'success':
      return '等待录入'
    case 'finished':
      return '录入完成'
    case 'error':
      return '解析失败'
    default:
      return '未知状态'
  }
}

// 获取状态样式类
const getStatusClass = (status) => {
  switch (status) {
    case 'processing':
      return 'processing-tag'
    case 'success':
      return 'success-tag'
    case 'finished':
      return 'finished-tag'
    case 'error':
      return 'error-tag'
    default:
      return ''
  }
}

// 格式化标题：移除最后一个冒号及后面的内容
const formatTitle = (title) => {
  if (!title) return '';
  // 查找最后一个冒号的位置
  const lastColonIndex = title.lastIndexOf(':');
  // 如果存在冒号，截取冒号前的部分；否则返回原始标题
  return lastColonIndex > -1 ? title.substring(0, lastColonIndex) : title;
};

// 点击历史记录项跳转
const handleHistoryClick = (item) => {
  // 检查状态是否为"解析成功"
  if (item.status === 'processing') {
    ElMessage.error('试卷解析中，请耐心等待！');
    return;
  }

  if (item.status === 'error') {
    ElMessage.error('试卷解析失败，请重试！');
    return;
  }

  // 状态为"解析成功"时才进行跳转
  router.push("/qh/questionEntry-detail/index/" + item.title);
}

// 获取历史记录函数
const fetchHistory = async () => {
  try {
    const response = await uploadStatus()
    if (response.code === 200) {
      // 直接使用后端返回的数组数据
      historyRecords.value = response.data
    } else {
      ElMessage.error('获取历史记录失败')
    }
  } catch (error) {
    console.error('获取历史记录出错:', error)
  }
}

// 页面加载时获取历史记录并设置定时器
onMounted(() => {
  // 初始加载
  fetchHistory()

  // 设置定时器，每2秒刷新一次
  refreshTimer.value = setInterval(() => {
    fetchHistory()
  }, 2000)
})

// 组件卸载时清除定时器
onUnmounted(() => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = null
  }
})

const loading = ref(false)
const examInfo = reactive({
  title: '',
  type: '',
  year: '',
  area: '',
  grade: '',
  subject: ''
})
const questions = ref([])
const currentIndex = ref(0)
// 存储上传的文件信息
const uploadedFile = ref(null)

const {proxy} = getCurrentInstance();
const {
  sys_qh_difficulty,
  sys_qh_questions_type,
  sys_qh_paper_type
} = proxy.useDict("sys_qh_difficulty", "sys_qh_questions_type", "sys_qh_paper_type");

// 当前题目计算属性
const currentQuestion = computed(() => {
  if (questions.value.length > 0 && currentIndex.value < questions.value.length) {
    return questions.value[currentIndex.value]
  }
  return null
})

// 已提交题目数量
const submittedCount = computed(() => {
  return questions.value.filter(q => q.submitted).length
})

// 已提交题目百分比
const submittedPercentage = computed(() => {
  if (questions.value.length === 0) return 0
  return Math.round((submittedCount.value / questions.value.length) * 100)
})

// 是否全部提交
const allSubmitted = computed(() => {
  return submittedCount.value === questions.value.length && questions.value.length > 0
})

// 自定义进度条颜色
const customColors = [
  {color: '#f56c6c', percentage: 20},
  {color: '#e6a23c', percentage: 40},
  {color: '#5cb87a', percentage: 60},
  {color: '#1989fa', percentage: 80},
  {color: '#6f7ad3', percentage: 100}
]

// 处理文件上传 - 异步调用后端接口
const handleUpload = async (file) => {
  // 保存上传的文件信息（前端临时URL）
  uploadedFile.value = {
    name: file.name,
    url: URL.createObjectURL(file.raw),
    type: file.type
  }

  // 显示提交成功消息
  ElMessage.success('已提交解析，请等待处理完成')

  try {
    // 调用后端接口
    const response = await uploadPaperFile(file.raw)

    // 立即刷新历史记录
    await fetchHistory()

  } catch (error) {
    console.error('上传失败:', error)
    ElMessage.error('上传失败，请检查网络或联系管理员')
  }
}

// 重置上传状态
const resetUpload = () => {
  if (uploadedFile.value?.url && uploadedFile.value.url.startsWith('blob:')) {
    URL.revokeObjectURL(uploadedFile.value.url)
  }
  uploadedFile.value = null
  questions.value = []
  currentIndex.value = 0
  Object.keys(examInfo).forEach(key => {
    examInfo[key] = ''
  })
}

// 获取图片URL（处理网络URL和本地路径）
const getImageUrl = (path) => {
  // 如果是网络URL直接返回
  if (path && (path.startsWith('http://') || path.startsWith('https://'))) {
    return path
  }

  // 处理后端返回的相对路径
  return path ? import.meta.env.VITE_APP_BASE_API + path : ''
}

// 处理图片上传
const handleImageUpload = (file, type) => {
  if (!currentQuestion.value) return

  // 模拟图片上传处理
  const reader = new FileReader()
  reader.onload = (e) => {
    if (type === 'question') {
      currentQuestion.value.questionImage = e.target.result
    } else {
      currentQuestion.value.answerImage = e.target.result
    }
    ElMessage.success(`${type === 'question' ? '题目' : '答案'}图片上传成功`)
  }
  reader.readAsDataURL(file.raw)
}

// 处理图片加载失败
const handleImageError = (type) => {
  ElMessage.warning(`${type === 'question' ? '题目' : '答案'}图片加载失败`)
}

// 提交当前题目
const submitQuestion = () => {
  if (currentQuestion.value) {
    currentQuestion.value.submitted = true
    ElMessage.success(`第 ${currentIndex.value + 1} 题提交成功！`)

    // 自动跳转到下一题
    if (currentIndex.value < questions.value.length - 1) {
      setTimeout(() => {
        currentIndex.value++
      }, 800)
    }
  }
}

// 提交全部
const submitAll = () => {
  questions.value.forEach(q => q.submitted = true)
  ElMessage.success('所有题目已成功提交！')
}

// 下一题
const nextQuestion = () => {
  if (currentIndex.value < questions.value.length - 1) {
    currentIndex.value++
  }
}

// 上一题
const prevQuestion = () => {
  if (currentIndex.value > 0) {
    currentIndex.value--
  }
}
</script>

<style scoped lang="scss">
.app-container {
  position: relative;
  padding: 20px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

// 历史记录样式
.history-card {
  margin-bottom: 20px;
}

.history-header {
  display: flex;
  align-items: center;
  font-weight: bold;
  font-size: 16px;
}

.search-input {
  margin-bottom: 15px;
}

.history-list {
  margin-top: 10px;
  max-height: 400px;
  overflow-y: auto;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 10px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: #f5f7fa;
  }
}

.file-name {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: 10px;
}

.right-group {
  display: flex;
  align-items: center;
  gap: 15px; /* 增加元素间距 */
}

.upload-time {
  color: #606266;
  font-size: 13px;
  white-space: nowrap;
}

.status-tag {
  font-size: 12px;
  padding: 3px 8px;
  border-radius: 4px;
  white-space: nowrap;
}

.processing-tag {
  background-color: #e6f7ff;
  color: #1890ff;
}

.success-tag {
  background-color: #f0f9eb;
  color: #52c41a;
}

.finished-tag {
  background-color: #f0f9eb;
  color: #052df1;
}

.error-tag {
  background-color: #fff2f0;
  color: #f5222d;
}

.arrow-icon {
  color: #c0c4cc;
  margin-left: 5px;
}

.empty-state {
  margin: 50px 0;
  text-align: center;
}

.upload-card {
  margin-bottom: 20px;
  border-radius: 4px;
}

.upload-area {
  padding: 0px 0;
  text-align: center;
  background-color: #fafafa;
  border-radius: 4px;
  border: 1px dashed #d9d9d9;
  transition: border-color 0.3s;

  &:hover {
    border-color: #409eff;
  }
}

.el-icon--upload {
  font-size: 60px;
  color: #8c939d;
  margin-bottom: 20px;
}

.el-upload__text {
  font-size: 16px;
  color: #606266;

  em {
    color: #409eff;
    font-style: normal;
  }
}

.el-upload__tip {
  color: #8c939d;
  margin-top: 10px;
  margin-bottom: 10px;
}

.file-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 10px 15px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.uploaded-file {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.file-icon {
  color: #409eff;
}

.content-container {
  margin-top: 20px;
}

.card-header {
  padding: 12px 16px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
  font-weight: 600;
  color: #303133;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.question-counter {
  font-size: 14px;
  color: #606266;
}

.info-form, .question-form {
  padding: 20px;
}

.form-row-margin {
  margin-bottom: 18px;
}

.image-container {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 10px;
  background-color: #fafafa;
}

.image-preview {
  width: 100%;
  min-height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  overflow: hidden;
  background-color: #fff;
  border: 1px solid #eee;

  img {
    max-width: 100%;
    max-height: 200px;
  }
}

.placeholder {
  color: #8c939d;
  text-align: center;
  padding: 30px 0;
}

.action-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.stats-bar {
  margin-top: 20px;
  padding: 15px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stats-info {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  color: #606266;

  .submitted {
    color: #52c41a;
  }

  .percentage {
    color: #409eff;
    font-weight: 500;
  }
}
</style>
