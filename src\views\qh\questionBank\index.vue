<template>
  <div class="app-container">
    <!-- 固定定位的试题栏按钮 -->
    <div
        class="floating-button"
        @click="openQuestionDrawer"
    >
      <el-button circle class="button-icon" type="primary">
        <el-icon>
          <FolderOpened/>
        </el-icon>
        <span v-if="boardCount > 0" class="badge">{{ boardCount }}</span>
      </el-button>
      <div class="button-text">试题栏</div>
    </div>

    <el-row :gutter="24">
      <!--内容树状数据-->
      <el-col :span="5" :xs="24" style="padding: 0">
        <div v-loading="treeLoading"
             element-loading-text="正在加载知识树数据..."
             element-loading-background="rgba(255, 255, 255, 0.9)"
             class="head-container">
          <!-- 添加下拉框和输入框 -->
          <div class="tree-search-container">
            <el-select
                v-model="queryParams.topNode"
                class="library-select"
                clearable
                placeholder="请选择题库"
                @change="handleTreeSelectChange"
            >
              <el-option
                  v-for="item in knowledgeTreeSelectOptions"
                  :key="item.id"
                  :label="item.label"
                  :value="item.id"
              />
            </el-select>
            <el-input
                v-model="filterText"
                class="filter-input"
                clearable
                placeholder="搜索知识点"
                prefix-icon="Search"
            />
          </div>

          <div class="tree-container">
            <el-empty v-if="!treeLoading && !queryParams.topNode" description="请先选择题库"></el-empty>
            <el-tree
                v-else-if="!treeLoading && currentTreeData.length > 0"
                ref="knowledgeTreeRef"
                :data="currentTreeData"
                :expand-on-click-node="false"
                :filter-node-method="filterNode"
                :props="{ label: 'name', children: 'children' }"
                class="knowledge-tree"
                highlight-current
                node-key="id"
                @node-click="handleNodeClick"
            >
              <template #default="{ node, data }">
                <span class="tree-node-content">
                  <span class="node-label">{{ node.label }}</span>
                </span>
              </template>
            </el-tree>
            <el-empty v-else-if="!treeLoading && currentTreeData.length === 0 && queryParams.topNode" description="暂无知识点数据"></el-empty>
          </div>
        </div>
      </el-col>
      <el-col :span="19" :xs="24">
        <!--试题查询-->
        <el-form v-show="showSearch" ref="queryRef" :inline="true" :model="queryParams" label-width="68px">
          <el-form-item label="难度选择">
            <el-select v-model="queryParams.difficultyList" clearable multiple placeholder="难度选择"
                       style="width: 200px">
              <el-option v-for="dict in sys_qh_difficulty" :key="dict.value" :label="dict.label" :value="dict.value"/>
            </el-select>
          </el-form-item>
          <el-form-item label="题型选择">
            <el-select v-model="queryParams.questionTypeList" clearable multiple placeholder="题型选择"
                       style="width: 200px">
              <el-option v-for="dict in sys_qh_questions_type" :key="dict.value" :label="dict.label"
                         :value="dict.value"/>
            </el-select>
          </el-form-item>
          <el-form-item label="试卷类型">
            <el-select v-model="queryParams.paperTypeList" clearable multiple placeholder="卷型选择"
                       style="width: 200px">
              <el-option v-for="dict in sys_qh_paper_type" :key="dict.value" :label="dict.label"
                         :value="dict.value"/>
            </el-select>
          </el-form-item>
          <el-form-item label="试卷来源">
            <el-input v-model="queryParams.sourcePaper" clearable placeholder="请输入试卷来源"
                      style="width: 200px"
                      @keyup.enter="handleQuery"/>
          </el-form-item>
        </el-form>
        <!--试题查询-->
        <el-form v-show="showSearch" ref="queryRef" :inline="true" :model="queryParams" label-width="68px">
          <el-form-item label="试题年份" style="width: 270px">
            <el-date-picker
                v-model="queryParams.yearList"
                range-separator="-"
                type="years"
                value-format="YYYY"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="试题地区">
            <el-cascader
                v-model="queryParams.regionList"
                :options="regionData"
                :props="{ value: 'value', label: 'label', children: 'children' }"
                clearable
                placeholder="请输入地区"
                class="info-select"
                style="width: 200px"
                @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="试题标签">
            <el-input v-model="queryParams.tag" clearable placeholder="请输入标签"
                      style="width: 200px"
                      @keyup.enter="handleQuery"/>
          </el-form-item>
          <el-form-item label="关键词">
            <el-input v-model="queryParams.keyword" clearable placeholder="请输入关键词"
                      style="width: 200px"
                      @keyup.enter="handleQuery"/>
          </el-form-item>
          <el-form-item>
            <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            <el-button v-hasPermi="['qh:questionBank:add']" icon="Plus" type="success" @click="handleAddQuestion">试题录入</el-button>
          </el-form-item>
        </el-form>
        <div v-loading="loading"
             element-loading-text="正在加载试题数据..."
             element-loading-background="rgba(255, 255, 255, 0.9)"
             class="scroll-container">
          <!-- 试题展示 -->
          <div class="question-list" style="border:none">
            <div v-if="!loading && questionBankList.length === 0" class="empty-question-container">
              <el-empty description="暂无试题">
                <template #description>
                  <p class="empty-description">暂无试题内容，快去给题库添加试题吧！</p>
                </template>
              </el-empty>
            </div>
            <transition-group v-if="!loading && questionBankList.length > 0" name="list">
              <div
                  v-for="(row, index) in questionBankList"
                  :key="row.id"
                  class="question-item"
              >
                <div class="cell">
                  <!-- 在template部分修改meta-info结构 -->
                  <div class="meta-info" style="padding-left: 10px; padding-right: 4px">
                    <span>{{ index + 1 }}.</span>
                    <div class="tags-container">
                      <dict-tag
                          :options="sys_qh_questions_type"
                          :value="row.questionType"
                          class="type-tag unified-tag"
                      />
                      <dict-tag
                          :options="sys_qh_difficulty"
                          :value="row.difficulty"
                          class="difficulty-tag unified-tag"
                      />
                    </div>
                    <!-- 知识点: -->
                    <div
                        v-if="row.knowledgeTreeList?.length > 0"
                        class="knowledge-box"
                    >
                      <span class="meta-info-label">知识点: </span>
                      <div class="knowledge-list">
                      <span
                          v-for="(item, idx) in row.knowledgeTreeList"
                          :key="idx"
                          class="knowledge-item"
                      >
                        {{ item?.name }}
                        <span
                            v-if="idx < row.knowledgeTreeList.length - 1"
                            class="separator"
                        >/</span>
                      </span>
                      </div>
                    </div>
                    <div class="button-group">
                      <el-button
                          v-hasPermi="['qh:questionBank:update']"
                          class="question-button"
                          icon="Edit"
                          plain
                          type="success"
                          @click="handleUploadAnalysis(row)"
                      >
                        上传解析
                      </el-button>
                      <!--                      <el-button-->
                      <!--                          v-hasPermi="['qh:questionBank:query']"-->
                      <!--                          class="question-button"-->
                      <!--                          icon="Download"-->
                      <!--                          plain-->
                      <!--                          type="warning"-->
                      <!--                          @click="handleExportPdf(row)"-->
                      <!--                      >-->
                      <!--                        试题导出-->
                      <!--                      </el-button>-->
                      <el-button
                          v-if="!row.inBoard"
                          v-hasPermi="['qh:questionBank:update']"
                          class="question-button"
                          icon="Plus"
                          plain
                          type="primary"
                          @click="addToQuestionBoard(row, $event)"
                      >
                        试题栏
                      </el-button>
                      <el-button
                          v-else
                          v-hasPermi="['qh:questionBank:update']"
                          class="question-button"
                          icon="Remove"
                          plain
                          type="danger"
                          @click="removeFromQuestionBoard(row.id)"
                      >
                        取消
                      </el-button>
                    </div>
                  </div>

                  <!-- 题干图片 -->
                  <div class="context-image">
                    <div class="index-image-wrapper">
                       <el-image
                          :hide-on-click-modal="true"
                          :src="row.context"
                          fit="contain"
                          oncontextmenu="return false;"
                      >
                        <template #error>
                          <div class="image-error">图片加载失败</div>
                        </template>
                      </el-image>
                    </div>
                  </div>
                  <div class="answer-control">
                    <!-- 试题来源试卷信息 -->
                    <span v-if="row.sourcePaper || row.year || row.region || row.tag" class="question-source">
                      <span v-if="row.sourcePaper" class="source-info-item">来源：{{ formatTitle(row.sourcePaper) }}</span>
                      <span v-if="row.year" class="source-info-item">年份：{{ row.year }}</span>
                      <span v-if="row.region" class="source-info-item">地区：{{ formatRegion(row.region) }}</span>
                      <span v-if="row.tag" class="source-tag-item">标签：{{ row.tag }}</span>
                    </span>
                    <el-button
                        v-if="row.questionAnalyze"
                        link
                        style="margin-left: auto; text-align: right"
                        type="primary"
                        @click="toggleAnswer(row)"
                    >
                      <el-icon>
                        <Hide v-if="row.showAnswer"/>
                        <Connection v-else/>
                      </el-icon>
                      {{ row.showAnswer ? '隐藏解析' : '展开解析' }}
                    </el-button>
                  </div>
                  <div>
                    <transition name="custom-zoom">
                      <div v-show="row.showAnswer" class="context-image">
                        <el-image
                            :hide-on-click-modal="true"
                            :src="row.questionAnalyze"
                            draggable="false"
                            fit="contain"
                            oncontextmenu="return false;"
                        >
                          <template #error>
                            <div class="image-error">图片加载失败</div>
                          </template>
                        </el-image>
                      </div>
                    </transition>
                  </div>
                </div>
              </div>
            </transition-group>
          </div>
        </div>

        <pagination v-show="total > 0" v-model:limit="queryParams.pageSize" v-model:page="queryParams.pageNum"
                    :total="total"
                    style="text-align: center; margin-top: 0px" @pagination="getList"/>
      </el-col>
    </el-row>

    <!-- 试题导入对话框 -->
    <el-dialog v-model="upload.open" :title="upload.title" append-to-body width="400px">
      <el-upload ref="uploadRef" :action="upload.url + '?updateSupport=' + upload.updateSupport" :auto-upload="false"
                 :disabled="upload.isUploading"
                 :headers="upload.headers" :limit="1"
                 :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" accept=".xlsx, .xls" drag>
        <el-icon class="el-icon--upload">
          <upload-filled/>
        </el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <div class="el-upload__tip">
              <el-checkbox v-model="upload.updateSupport"/>
              是否更新已经存在的试题数据
            </div>
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link :underline="false" style="font-size:12px;vertical-align: baseline;" type="primary"
                     @click="importTemplate">下载模板
            </el-link>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 试题栏抽屉 -->
    <el-drawer
        v-model="drawerVisible"
        :append-to-body="true"
        :with-header="false"
        class="question-drawer"
        direction="rtl"
        size="40%"
    >
      <div class="drawer-header">
        <span class="drawer-title">试题栏（{{ boardQuestions.length }}）</span>
        <div class="drawer-actions">
          <el-button :disabled="boardQuestions.length === 0" icon="Delete" size="small" type="danger"
                     @click="handleClearBoard">清空
          </el-button>
          <el-button :disabled="boardQuestions.length === 0" icon="Document" size="small" type="primary"
                     @click="handleManualPaper">手动组卷
          </el-button>
        </div>
      </div>
      <el-scrollbar height="calc(100% - 60px)">
        <div v-if="boardQuestions.length === 0" class="empty-tip">
          <el-empty description="暂无试题"/>
        </div>
        <draggable
            v-model="boardQuestions"
            :animation="150"
            :delay="50"
            :disabled="boardQuestions.length <= 1"
            class="draggable-container"
            item-key="id"
            @start="onDragStart"
            @end="onDragEnd"
        >
          <template #item="{element, index}">
            <div
                :key="element.id"
                class="selected-question"
            >
              <div class="question-header">
                <span class="drag-icon" title="拖动调整顺序"><el-icon><Operation/></el-icon></span>
                <span class="question-index">{{ index + 1 }}.</span>
                <div class="question-meta">
                  <dict-tag
                      :options="sys_qh_questions_type"
                      :value="element.questionType"
                      class="type-tag"
                  />
                  <dict-tag
                      :options="sys_qh_difficulty"
                      :value="element.difficulty"
                      class="difficulty-tag"
                  />
                  <span
                      v-for="(data, idx) in element.knowledgeTreeList"
                      :key="idx"
                      class="knowledge-item"
                  >
                        {{ data?.name }}
                        <span
                            v-if="idx < element.knowledgeTreeList.length - 1"
                            class="separator"
                        >/</span>
                      </span>
                </div>
                <el-button
                    circle
                    icon="Delete"
                    size="small"
                    type="danger"
                    @click="removeFromQuestionBoard(element.id)"
                />
              </div>
              <el-image
                  :src="element.context"
                  class="question-image"
                  fit="contain"
              >
                <template #error>
                  <div class="image-error">图片加载失败</div>
                </template>
              </el-image>
            </div>
          </template>
        </draggable>
      </el-scrollbar>
    </el-drawer>

    <!-- 上传解析对话框 -->
    <el-dialog
        v-model="uploadAnalysisVisible"
        append-to-body
        destroy-on-close
        title="上传解析"
        width="500px"
    >
      <div class="upload-analysis-container">
        <el-alert
            v-if="imageError"
            :title="imageError"
            class="mb-3"
            show-icon
            type="error"
        />
        <el-upload
            ref="uploadRef"
            :auto-upload="false"
            :limit="1"
            :on-change="handleFileChange"
            :show-file-list="true"
            accept="image/*"
            action="#"
            class="analysis-uploader"
        >
          <template v-if="!uploadFile">
            <div class="upload-area">
              <el-icon class="upload-icon">
                <Plus/>
              </el-icon>
              <div class="upload-text">点击上传解析图片</div>
              <!--              <div class="upload-tip">图片大小限制: 宽度≤1200px, 高度≤300px</div>-->
            </div>
          </template>
          <template v-else>
            <img v-if="previewImage" :src="previewImage" class="preview-image"/>
          </template>
        </el-upload>
        <div v-if="uploadFile" class="preview-actions">
          <el-button
              :loading="uploadLoading"
              type="primary"
              @click="submitUploadAnalysis"
          >确认上传
          </el-button>
          <el-button @click="cancelUpload">取消</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script name="questionBank" setup>
import {getToken} from "@/utils/auth";
import {
  addQuestionBankBoard,
  delQuestionBank,
  delQuestionBankBoard,
  listQuestionBank,
  listQuestionBankBoard,
  updateQuestionBank,
  updateQuestionBankBoardOrder
} from "@/api/qh/questionBank.js";
import {selectKnowledgeTreeList} from "@/api/qh/knowledgeTree.js";
import {onMounted, reactive, ref, toRefs, watch} from 'vue';
import {Connection, FolderOpened, Hide, Operation, Plus} from "@element-plus/icons-vue";
import {useDebounceFn} from '@vueuse/core';
import {fileUpload} from "@/api/qh/minio.js";
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import draggable from 'vuedraggable';
import {codeToText, regionData} from "element-china-area-data";
const router = useRouter();
const {proxy} = getCurrentInstance();
const {
  sys_qh_difficulty,
  sys_qh_questions_type,
  sys_qh_paper_type
} = proxy.useDict("sys_qh_difficulty", "sys_qh_questions_type", "sys_qh_paper_type");

// 页面状态
const questionBankList = ref([]); // 确保初始为空数组
const loading = ref(true); // 初始化为true，确保一开始就显示loading
const treeLoading = ref(true); // 初始状态设置为true，确保一开始就显示loading
const showSearch = ref(true);
const total = ref(0);
const dateRange = ref([]);
const knowledgeTreeOptions = ref([]); // 完整的知识树数据
const knowledgeTreeSelectOptions = ref([]); // 仅顶层的知识树数据
const currentTreeData = ref([]); // 当前选中题库的子节点
const filterText = ref('');
const drawerVisible = ref(false);
const boardQuestions = ref([]); // 存储试题栏中的题目
const boardCount = ref(0); // 试题栏数量

// 上传解析相关
const uploadAnalysisVisible = ref(false);
const currentQuestion = ref(null);
const previewImage = ref('');
const uploadRef = ref(null);
const uploadLoading = ref(false);
const imageError = ref('');
const uploadFile = ref(null);
// 添加拖拽相关状态
const isDropping = ref(false);
// 记录拖拽前的顺序
const originalBoardQuestions = ref([]);

/*** 试题导入参数 */
const upload = reactive({
  open: false,
  title: "",
  isUploading: false,
  updateSupport: 0,
  headers: {Authorization: "Bearer " + getToken()},
  url: import.meta.env.VITE_APP_BASE_API + "/system/user/importData"
});

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    sourcePaper: undefined,
    region: undefined,
    yearList: [],
    knowledgeTreeIds: [],
    difficultyList: [],
    questionTypeList: [],
    regionList: [],
    paperTypeList: [],
    topNode: '', // 存储选中的顶层节点ID
    keyword: '', // 关键词搜索
  },
  queryTreeParams: {
    parentId: '0',
    status: '0',
  }
});
const formatTitle = (title) => {
  if (!title) return '';
  // 查找最后一个冒号的位置
  const lastColonIndex = title.lastIndexOf(':');
  // 如果存在冒号，截取冒号前的部分；否则返回原始标题
  return lastColonIndex > -1 ? title.substring(0, lastColonIndex) : title;
};
const {queryParams, queryTreeParams} = toRefs(data);
// 修改地区格式化方法，兼容数组和字符串格式
const formatRegion = (regionData) => {
  // 处理空值情况
  if (!regionData) {
    return '';
  }

  // 统一将输入转换为数组（确保至少3个元素）
  let regionCodes = [];
  if (Array.isArray(regionData)) {
    // 数组格式：截取前3个元素（确保是省、市、区三级）
    regionCodes = regionData.slice(0, 3);
  } else if (typeof regionData === 'string') {
    // 字符串格式：用逗号分割后取前3个元素
    regionCodes = regionData.split(',').map(code => code.trim()).filter(code => code).slice(0, 3);
  }

  // 确保有3个编码（不足则补空，避免数组长度不够）
  while (regionCodes.length < 3) {
    regionCodes.push('');
  }

  // 调试用：打印原始编码（可根据实际输出调整）
  console.log('地区编码:', regionCodes);

  // 解析三级编码（省、市、区）
  const province = codeToText[regionCodes[0]] || ''; // 第一级：省
  const city = codeToText[regionCodes[1]] || '';     // 第二级：市
  const district = codeToText[regionCodes[2]] || ''; // 第三级：区

  // 处理特殊情况：如果市编码无效，但省编码有效（如直辖市）
  const validCity = city || province;
  // 拼接结果（过滤空值）
  const regionNames = [province, validCity, district].filter(name => name);

  // 特殊处理：如果只有一个有效名称（如直辖市）
  if (regionNames.length === 1) {
    return regionNames[0];
  }

  // 正常拼接为“省/市/区”
  return regionNames.join(' / ');
};


/** 通过条件过滤节点 */
const filterNode = (value, data) => {
  if (!value) return true;
  return data.name.toLowerCase().includes(value.toLowerCase());
};

/** 过滤知识树 */
const filterKnowledgeTree = () => {
  const knowledgeTreeRef = proxy.$refs.knowledgeTreeRef;
  if (knowledgeTreeRef) {
    knowledgeTreeRef.filter(filterText.value);
  }
};

// 监听过滤文本变化
watch(filterText, (val) => {
  filterKnowledgeTree();
});

/** 查询内容下拉树结构 */
function getKnowledgeTree() {
  // 设置树的loading状态
  treeLoading.value = true;

  return selectKnowledgeTreeList(queryTreeParams.value).then(response => {
    try {
      // 处理树状结构
      const treeData = proxy.handleTree(response.data, "id");

      // 顶层节点数据用于下拉选择
      knowledgeTreeSelectOptions.value = treeData.map(item => ({
        id: item.id,
        label: item.name
      }));

      // 默认选择第一个数据
      if (knowledgeTreeSelectOptions.value.length > 0) {
        queryParams.value.topNode = knowledgeTreeSelectOptions.value[0].id;
        // 确保设置knowledgeTreeIds
        queryParams.value.knowledgeTreeIds = [knowledgeTreeSelectOptions.value[0].id];

        // 获取默认选中节点的完整子树结构
        return selectKnowledgeTreeList({ ancestors: queryParams.value.topNode }).then(childResponse => {
          const fullTreeData = proxy.handleTree(childResponse.data, "id");

          // 找到选中的顶层节点，只展示其子节点
          const selectedNode = fullTreeData.find(node => node.id === queryParams.value.topNode);
          if (selectedNode && selectedNode.children) {
            currentTreeData.value = selectedNode.children;
          } else {
            currentTreeData.value = [];
          }

          // 关闭树loading状态
          treeLoading.value = false;
          return Promise.resolve();
        }).catch(() => {
          currentTreeData.value = [];
          // 出错时也关闭树loading状态
          treeLoading.value = false;
          return Promise.resolve();
        });
      }

      // 没有数据时关闭树loading状态
      treeLoading.value = false;
      return Promise.resolve();
    } catch (error) {
      console.error('处理知识树数据失败:', error);
      // 出错时关闭树loading状态
      treeLoading.value = false;
      return Promise.reject(error);
    }
  }).catch(error => {
    console.error('获取知识树失败:', error);
    // 出错时关闭树loading状态
    treeLoading.value = false;
    return Promise.reject(error);
  });
}

/** 处理树选择变化 */
function handleTreeSelectChange(val) {
  if (!val) {
    currentTreeData.value = [];
    queryParams.value.knowledgeTreeIds = [];
    return;
  }

  // 设置查询参数
  queryParams.value.knowledgeTreeIds = [val];

  // 设置树loading状态（先设置，确保loading立即显示）
  treeLoading.value = true;

  // 获取选中节点的完整子树结构
  selectKnowledgeTreeList({ ancestors: val }).then(response => {
    try {
      // 处理树状结构
      const fullTreeData = proxy.handleTree(response.data, "id");

      // 找到选中的顶层节点，只展示其子节点
      const selectedNode = fullTreeData.find(node => node.id === val);
      if (selectedNode && selectedNode.children) {
        currentTreeData.value = selectedNode.children;
      } else {
        currentTreeData.value = [];
      }

      // 先关闭树loading状态
      treeLoading.value = false;

      // 再触发查询，由getList统一处理loading状态
      getList();
    } catch (error) {
      console.error('处理知识树数据失败:', error);
      // 出错时关闭树loading状态
      treeLoading.value = false;
      // 出错时也调用getList以显示无数据状态
      getList();
    }
  }).catch(() => {
    currentTreeData.value = [];
    // 出错时关闭树loading状态
    treeLoading.value = false;
    // 出错时也调用getList以显示无数据状态
    getList();
  });
}

/** 节点单击事件 */
function handleNodeClick(data) {
  // 只更新查询参数，不修改树结构
  queryParams.value.knowledgeTreeIds = [data.id];

  // 调用getList获取数据，由getList统一处理loading状态
  getList();
}

/** 查询试题列表 */
function getList() {
  // 开启loading效果，依赖v-loading指令
  // 所有调用getList的地方都不需要再单独设置loading状态
  loading.value = true;

  // 清空列表数据，避免显示旧数据
  questionBankList.value = [];

  // 创建新的查询参数对象，排除topNode参数
  const queryParamsForAPI = {...queryParams.value};
  delete queryParamsForAPI.topNode;

  listQuestionBank(proxy.addDateRange(queryParamsForAPI, dateRange.value)).then(async res => {
    try {
      // 初始化展示状态
      const questionList = res.rows.map(item => ({
        ...item,
        showAnswer: false,
        inBoard: false  // 默认不在试题栏中
      }));

      // 获取试题栏中的题目
      try {
        const boardRes = await listQuestionBankBoard();
        if (boardRes.code === 200 && boardRes.data && boardRes.data.length > 0) {
          // 获取试题栏中所有题目的ID
          const boardQuestionIds = new Set(boardRes.data.map(item => item.id));

          // 标记主列表中在试题栏中的题目
          questionList.forEach(item => {
            if (boardQuestionIds.has(item.id)) {
              item.inBoard = true;
            }
          });

          // 更新试题栏数量
          boardCount.value = boardRes.data.length;
        }
      } catch (error) {
        console.error('获取试题栏数据失败:', error);
        // 不影响主流程
      }

      // 延迟关闭loading，确保DOM渲染完成并让用户看到loading效果
      // 使用统一的延迟时间，确保所有情况下的体验一致
      setTimeout(() => {
        // 更新列表数据
        questionBankList.value = questionList;
        total.value = res.total;
        loading.value = false;
      }, 300);
    } catch (error) {
      console.error('处理试题数据失败:', error);

      // 出现错误时设置空数据
      questionBankList.value = [];
      total.value = 0;

      // 与成功情况使用相同的延迟时间关闭loading
      setTimeout(() => {
        loading.value = false;
        proxy.$message.error('获取试题数据失败');
      }, 300);
    }
  }).catch((error) => {
    console.error('请求试题列表失败:', error);

    // 请求失败时设置空数据
    questionBankList.value = [];
    total.value = 0;

    // 与成功情况使用相同的延迟时间关闭loading
    setTimeout(() => {
      loading.value = false;
      proxy.$message.error('获取试题数据失败');
    }, 300);
  });
}

/** 搜索按钮操作 */
const handleQuery = useDebounceFn(() => {
  // 设置页码为第一页
  queryParams.value.pageNum = 1;

  // 调用getList获取数据，由getList统一处理loading状态
  getList();
}, 300);

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  queryParams.value.sourcePaper = undefined;
  queryParams.value.yearList = [];
  queryParams.value.region = undefined;
  queryParams.value.knowledgeTreeIds = [];
  queryParams.value.difficultyList = [];
  queryParams.value.questionTypeList = [];
  queryParams.value.regionList = [];
  queryParams.value.paperTypeList = [];
  queryParams.value.tag = undefined;
  queryParams.value.keyword = undefined;

  // 如果有数据，重新选择第一个
  if (knowledgeTreeSelectOptions.value.length > 0 && queryParams.value.topNode) {
    // 不要重置已经选择的题库信息，只更新查询参数
    queryParams.value.knowledgeTreeIds = [queryParams.value.topNode];
    // 直接调用查询，跳过树loading
    getList();
  } else {
    // 调用搜索方法获取数据，由getList统一处理loading状态
    handleQuery();
  }
}

/** 删除按钮操作 */
function handleDelete(id) {
  proxy.$modal.confirm('是否确认删除该试题？').then(function () {
    return delQuestionBank(id);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {
  });
}

/** 导入按钮操作 */
function handleImport() {
  upload.title = "试题导入";
  upload.open = true;
}

/** 下载模板操作 */
function importTemplate() {
  proxy.download("system/user/importTemplate", {}, `user_template_${new Date().getTime()}.xlsx`);
}

/**文件上传中处理 */
const handleFileUploadProgress = () => {
  upload.isUploading = true;
};

/** 文件上传成功处理 */
const handleFileSuccess = (response, file) => {
  upload.open = false;
  upload.isUploading = false;
  proxy.$refs["uploadRef"].handleRemove(file);
  proxy.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", {dangerouslyUseHTMLString: true});
  getList();
};

/** 提交上传文件 */
function submitFileForm() {
  proxy.$refs["uploadRef"].submit();
}

// 试题栏相关功能
// 打开试题栏抽屉并获取试题栏数据
const openQuestionDrawer = async () => {
  drawerVisible.value = true;
  await getQuestionBoardList(false);
};

// 获取试题栏数据
const getQuestionBoardList = async (useGlobalLoading = false) => {
  try {
    // 只在确实需要时才显示主列表的loading
    if (useGlobalLoading) {
      loading.value = true;
    }

    const res = await listQuestionBankBoard();
    if (res.code === 200) {
      // 直接更新数据，不需要延迟
      boardQuestions.value = res.data || [];
      // 更新试题栏数量
      boardCount.value = boardQuestions.value.length;

      // 关闭主列表loading(如果设置了)
      if (useGlobalLoading) {
        loading.value = false;
      }
    } else {
      proxy.$message.error(res.msg || '获取试题栏数据失败');

      // 关闭主列表loading(如果设置了)
      if (useGlobalLoading) {
        loading.value = false;
      }
    }
  } catch (error) {
    console.error('获取试题栏数据失败:', error);
    proxy.$message.error('获取试题栏数据失败');

    // 关闭主列表loading(如果设置了)
    if (useGlobalLoading) {
      loading.value = false;
    }
  }
};

// 添加试题到试题栏
const addToQuestionBoard = async (question, event) => {
  try {
    // 获取最近的按钮组容器
    const buttonGroup = event?.target?.closest('.button-group');

    // 使用统一的loading效果
    const buttonLoading = proxy.$loading({
      target: buttonGroup || document.body,
      text: '添加至试题栏...',
      background: 'rgba(255, 255, 255, 0.9)'
    });

    const res = await addQuestionBankBoard(question.id);
    if (res.code === 200) {
      proxy.$message.success('已添加到试题栏');
      // 更新试题的inBoard状态
      question.inBoard = true;
      // 更新试题栏数量，但不刷新主列表
      const boardRes = await listQuestionBankBoard();
      if (boardRes.code === 200) {
        boardQuestions.value = boardRes.data || [];
        boardCount.value = boardQuestions.value.length;
      }
    } else {
      proxy.$message.error(res.msg || '添加到试题栏失败');
    }
    buttonLoading.close();
  } catch (error) {
    console.error('添加到试题栏失败:', error);
    proxy.$message.error('添加到试题栏失败');
  }
};

// 从试题栏移除试题
const removeFromQuestionBoard = async (id) => {
  try {
    // 区分是从主列表中移除还是从试题栏中移除
    const isFromDrawer = drawerVisible.value && document.querySelector('.question-drawer')?.contains(event?.target);
    let loadingInstance;

    if (isFromDrawer) {
      // 如果是从抽屉中移除，使用局部loading
      loadingInstance = proxy.$loading({
        target: document.querySelector('.question-drawer'),
        text: '从试题栏中移除...',
        background: 'rgba(255, 255, 255, 0.9)'
      });
    } else {
      // 如果是从主列表中移除，只在按钮区域显示loading
      loadingInstance = proxy.$loading({
        target: event?.target?.closest('.button-group') || document.body,
        text: '从试题栏中移除...',
        background: 'rgba(255, 255, 255, 0.9)'
      });
    }

    const res = await delQuestionBankBoard(id);
    if (res.code === 200) {
      proxy.$message.success('已从试题栏移除');

      // 更新当前列表中对应试题的inBoard状态
      const question = questionBankList.value.find(item => item.id === id);
      if (question) {
        question.inBoard = false;
      }

      // 更新试题栏数据，但不刷新主列表
      const boardRes = await listQuestionBankBoard();
      if (boardRes.code === 200) {
        boardQuestions.value = boardRes.data || [];
        boardCount.value = boardQuestions.value.length;
      }
    } else {
      proxy.$message.error(res.msg || '从试题栏移除失败');
    }

    loadingInstance.close();
  } catch (error) {
    console.error('从试题栏移除失败:', error);
    proxy.$message.error('从试题栏移除失败');
  }
};

// 清空试题栏
const handleClearBoard = async () => {
  try {
    await proxy.$confirm('确定要清空试题栏吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    // 不使用loading效果

    const res = await delQuestionBankBoard('all');
    if (res.code === 200) {
      proxy.$message.success('试题栏已清空');

      // 更新所有试题的inBoard状态为false
      questionBankList.value.forEach(item => {
        item.inBoard = false;
      });

      // 直接更新试题栏数据
      boardQuestions.value = [];
      boardCount.value = 0;
    } else {
      proxy.$message.error(res.msg || '清空试题栏失败');
    }

  } catch (error) {
    if (error !== 'cancel') {
      console.error('清空试题栏失败:', error);
      proxy.$message.error('清空试题栏失败');
    }
  }
};

// 手动组卷
const handleManualPaper = () => {
  router.push('/qh/testhand/index');
};

// 切换解析显示
const toggleAnswer = (row) => {
  row.showAnswer = !row.showAnswer;
};

// 处理上传解析
const handleUploadAnalysis = (row) => {
  currentQuestion.value = row;
  uploadAnalysisVisible.value = true;
  previewImage.value = '';
  uploadFile.value = null;
  imageError.value = '';
};

// 文件选择处理
const handleFileChange = (file) => {
  if (!file) return;
  uploadFile.value = file;

  // 创建图片对象检查尺寸
  const img = new Image();
  img.onload = () => {
    // if (img.width > 1200 || img.height > 300) {
    //   imageError.value = `图片尺寸超出限制，当前尺寸：${img.width}x${img.height}，最大尺寸：1200x300`;
    //   uploadRef.value.clearFiles();
    //   uploadFile.value = null;
    //   return;
    // }

    imageError.value = '';
    // 生成预览
    previewImage.value = URL.createObjectURL(file.raw);
  };

  img.onerror = () => {
    imageError.value = '图片加载失败，请重新选择';
    uploadRef.value.clearFiles();
    uploadFile.value = null;
  };

  // 加载图片以触发验证
  img.src = URL.createObjectURL(file.raw);
};

// 取消上传
const cancelUpload = () => {
  previewImage.value = '';
  imageError.value = '';
  uploadFile.value = null;
  uploadRef.value.clearFiles();
};

// 提交上传解析
const submitUploadAnalysis = async () => {
  if (!uploadFile.value || !currentQuestion.value) {
    proxy.$message.warning('请先选择图片');
    return;
  }

  try {
    uploadLoading.value = true;

    // 直接传递文件对象给上传接口
    const res = await fileUpload(uploadFile.value.raw);
    if (res.code === 200) {
      const imageUrl = res.msg;
      // 更新试题解析
      await updateQuestionBank({
        id: currentQuestion.value.id,
        questionAnalyze: imageUrl
      });

      // 关闭对话框并刷新列表
      uploadAnalysisVisible.value = false;
      previewImage.value = '';
      uploadFile.value = null;
      uploadLoading.value = false;
      proxy.$message.success('解析上传成功');
      getList();
    } else {
      uploadLoading.value = false;
      proxy.$message.error(res.msg || '图片上传失败');
    }
  } catch (error) {
    console.error('上传解析失败:', error);
    uploadLoading.value = false;
    proxy.$message.error('上传解析失败');
  }
};

// 导出PDF函数
const handleExportPdf = async (row) => {
  try {
    // 显示加载提示
    proxy.$modal.loading("正在导出试题，请稍候...");

    // 检查题干图片是否存在
    if (!row || !row.context) {
      proxy.$modal.closeLoading();
      proxy.$modal.msgError("试题数据不完整，无法导出");
      return;
    }

    // 创建临时容器
    const container = document.createElement('div');
    container.style.position = 'absolute';
    container.style.top = '-9999px';
    container.style.left = '-9999px';
    container.style.width = '800px'; // 固定宽度
    document.body.appendChild(container);

    try {
      // 创建题干内容
      const questionDiv = document.createElement('div');
      questionDiv.style.marginBottom = '20px';
      questionDiv.style.pageBreakInside = 'avoid';

      // 添加题干图片
      const questionImg = document.createElement('img');
      questionImg.src = row.context;
      questionImg.style.width = '100%';
      questionImg.style.maxWidth = '800px';
      questionDiv.appendChild(questionImg);
      container.appendChild(questionDiv);

      // 如果有解析，添加解析内容
      if (row.questionAnalyze) {
        // 添加分隔线
        const separator = document.createElement('hr');
        separator.style.margin = '20px 0';
        separator.style.border = '1px dashed #ccc';
        container.appendChild(separator);

        // 添加解析标题
        const answerTitle = document.createElement('h3');
        answerTitle.textContent = '参考答案与解析';
        answerTitle.style.color = '#409EFF';
        answerTitle.style.marginBottom = '10px';
        container.appendChild(answerTitle);

        // 添加解析图片
        const answerImg = document.createElement('img');
        answerImg.src = row.questionAnalyze;
        answerImg.style.width = '100%';
        answerImg.style.maxWidth = '800px';
        container.appendChild(answerImg);
      }

      // 等待图片加载完成
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 使用html2canvas将容器转为图片
      const canvas = await html2canvas(container, {
        allowTaint: true,
        useCORS: true,
        logging: false,
        scale: 2, // 提高清晰度
        imageTimeout: 5000, // 增加图片加载超时时间
        onclone: (clonedDoc) => {
          // 确保克隆的文档中的图片已完全加载
          const images = clonedDoc.getElementsByTagName('img');
          for (let i = 0; i < images.length; i++) {
            images[i].onload = () => console.log('图片已加载');
            images[i].onerror = () => console.error('图片加载失败');
          }
        }
      });

      // 创建PDF
      const imgData = canvas.toDataURL('image/jpeg', 1.0);
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'px',
        format: [canvas.width, canvas.height]
      });

      // 计算PDF页面尺寸以适应完整图像
      const imgProps = pdf.getImageProperties(imgData);
      const pdfWidth = pdf.internal.pageSize.getWidth();
      const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;

      // 将图像添加到PDF
      pdf.addImage(imgData, 'JPEG', 0, 0, pdfWidth, pdfHeight);

      // 生成文件名
      let fileName = '试题导出.pdf';

      try {
        const knowledgeNames = row.knowledgeTreeList && row.knowledgeTreeList.length > 0
            ? row.knowledgeTreeList.map(item => item?.name || '').filter(Boolean).join('-')
            : '未分类';
        const difficulty = row.difficulty !== undefined && sys_qh_difficulty
            ? proxy.selectDictLabel(sys_qh_difficulty, row.difficulty) || '未知难度'
            : '未知难度';

        fileName = `试题-${knowledgeNames}-${difficulty}.pdf`;
      } catch (fileNameError) {
        console.error('生成文件名出错:', fileNameError);
        fileName = `试题导出-${new Date().getTime()}.pdf`;
      }

      // 保存PDF
      pdf.save(fileName);

      // 关闭加载提示
      proxy.$modal.closeLoading();

      // 显示成功提示
      proxy.$modal.msgSuccess("试题导出成功");
    } finally {
      // 确保移除临时容器
      if (container && container.parentNode) {
        document.body.removeChild(container);
      }
    }
  } catch (error) {
    console.error('导出PDF失败:', error);
    proxy.$modal.closeLoading();
    proxy.$modal.msgError(`导出失败: ${error.message || '未知错误'}`);
  }
};

// 添加拖拽开始处理方法
const onDragStart = () => {
  // 拖拽开始时保存原始顺序
  originalBoardQuestions.value = [...boardQuestions.value];
};

// 添加拖拽结束处理方法
const onDragEnd = async () => {
  try {
    // 比较拖拽前后顺序是否有变化
    const hasOrderChanged = originalBoardQuestions.value.some((item, index) => {
      return item.id !== boardQuestions.value[index]?.id;
    });

    // 如果顺序没有变化，则不调用后端接口
    if (!hasOrderChanged) {
      console.log('顺序未变化，无需更新');
      return;
    }

    // 组装更新后的顺序数据
    const orderData = boardQuestions.value.map((item, index) => ({
      id: item.id,
      orderNum: index + 1
    }));

    // 不使用loading效果

    const res = await updateQuestionBankBoardOrder(orderData);
    if (res.code === 200) {
      proxy.$message.success('试题顺序已更新');
      // 不需要刷新主列表
    } else {
      proxy.$message.error(res.msg || '试题顺序更新失败');
      // 更新失败时仅重新获取试题栏列表，使用false参数避免全局loading
      await getQuestionBoardList(false);
    }

  } catch (error) {
    console.error('更新试题顺序失败:', error);
    proxy.$message.error('更新试题顺序失败');
    // 发生错误时仅重新获取试题栏列表，使用false参数避免全局loading
    await getQuestionBoardList(false);
  }
};

// 跳转到试题录入页面
const handleAddQuestion = () => {
  router.push('/qh/questionEntry-index/index');
};

onMounted(() => {
  // 确保知识树loading在组件挂载时已经显示
  treeLoading.value = true;

  // 先获取知识树，然后在回调中获取试题列表
  getKnowledgeTree().then(() => {
    // 确保知识树数据加载完成后再获取试题列表
    // getList函数会设置loading状态
    getList();
  }).catch(error => {
    console.error('获取知识树数据失败:', error);
    // 发生错误时也调用getList以显示无数据状态
    getList();
  });

  // 获取试题栏数据 - 使用false参数避免额外设置loading
  getQuestionBoardList(false);
});
</script>

<style scoped>
.app-container {
  display: flex;
  flex-direction: column;

  margin-bottom: 0;
  padding-right: 80px;
  overflow-y: auto;
}

/* 知识树样式 */
.head-container {
  border: 1px solid #ebeef5;
  border-radius: 6px;
  padding: 5px;
  background: #fff;
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  position: relative; /* 确保相对定位以支持loading效果 */
  min-height: 200px; /* 添加最小高度确保loading显示 */
}

.head-container:hover {
  border-color: #409eff;
  box-shadow: 0 2px 2px 0 rgba(64, 158, 255, 0.1);
}

.tree-search-container {
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.library-select {
  width: 100%;
  margin-bottom: 10px;
  font-size: 14px;
}

.filter-input {
  width: 100%;
  margin-bottom: 10px;
  font-size: 14px;
}

.tree-container {
  height: calc(100vh - 270px);
  overflow-y: auto;
  /*border: 1px solid #ebeef5;*/
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 22px;
}

.tree-node-content {
  display: flex;
  align-items: center;
  width: 100%;
}

.node-label {
  margin-left: 5px;
  font-size: 14px;
  line-height: 1.5;
}

.knowledge-tree {
  font-size: 14px;
  font-weight: 500;
  padding-right: 1px;
}

.knowledge-tree :deep(.el-tree-node__content) {
  height: 36px;
  padding: 6px 0;
  margin: 2px 0;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.knowledge-tree :deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #ecf5ff;
  font-weight: bold;
  color: #409eff;
  border-left: 3px solid #409eff;
}

.knowledge-tree :deep(.el-tree-node__content:hover) {
  background-color: #f5f7fa;
  transform: translateX(3px);
}

.knowledge-tree :deep(.el-tree-node__children) {
  padding-left: 16px;
}

/* 滚动效果 */
.head-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  background-color: transparent;
  transition: all 0.3s 0.2s;
}

.head-container::-webkit-scrollbar-thumb {
  background-color: rgba(192, 196, 204, 0);
  border-radius: 4px;
  transition: all 0.3s ease;
}

.head-container:hover::-webkit-scrollbar {
  background-color: #f5f7fa;
}

.head-container:hover::-webkit-scrollbar-thumb {
  background-color: #c0c4cc;
}

/* 试题列表样式 */
.scroll-container {
  height: calc(100vh - 270px);
  overflow-y: auto;
  border-radius: 8px;
  padding-right: 0;
  padding-left: 0;
  margin: 0;
  position: relative; /* 添加相对定位以支持loading定位 */
}

.meta-info {
  display: flex;
  align-items: center;
  gap: 5px;
  background: #f8fbff;
  border-radius: 6px 6px 0 0;
  justify-content: space-between;
  margin-bottom: 0;
  padding: 8px 15px;
  border-bottom: 1px solid #ebeef5;
}

.type-tag,
.difficulty-tag {
  font-size: 18px;
  font-weight: 520;
  padding: 6px 5px;
  transform: scale(1.15);
}

.knowledge-box {
  background: #e6f7ff;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 !important;
}

.knowledge-box .meta-info-label {
  color: #666;
}

.knowledge-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.knowledge-item {
  display: inline-flex;
  align-items: center;
  font-size: 14px;
  color: #409eff;
  padding: 2px 1px;
  border-radius: 3px;
}

.separator {
  color: #c0c4cc;
  margin-left: 8px;
}

.button-group {
  display: flex;
  gap: 10px;
  margin-left: auto;
  font-size: 12px;
}

.question-button {
  width: 90px;
  height: 27px;
  margin: auto;
  border-radius: 4px;
  padding: 8px 15px;
  transition: all 0.3s;
}

.upload-analysis-btn {
  background-color: #e8f5e9 !important;
  border-color: #c8e6c9 !important;
  color: #2e7d32 !important;
  border-radius: 4px;
  transition: all 0.3s;
  width: 90px;
  height: 27px;
  margin: auto;
}

.upload-analysis-btn:hover {
  background-color: #d4edda !important;
  border-color: #c3e6cb !important;
  color: #155724 !important;
}

.upload-analysis-btn:active {
  background-color: #c3e6cb !important;
}

.context-image {
  border-radius: 4px;
  border: none;
  display: flex;
  justify-content: left;
  float: none;
  align-items: flex-start;
  position: relative;
  max-width: 800px;
  min-width: 600px;
  margin: 15px auto;
  padding: 0 30px;
}

.index-image-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 1px;
}

.question-index {
  position: absolute;
  top: 17px;
  left: 20px;
  color: #333;
  font-size: 16px;
  z-index: 10;
}

.tags-container {
  display: flex;
  align-items: center;
  gap: 10px;
  justify-content: flex-start;
  flex-wrap: wrap;
  padding-right: 5px;
}

@media (max-width: 768px) {
  .tags-container {
    gap: 8px;
  }
}

.image-error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #909399;
  font-size: 14px;
}

.empty-description {
  font-size: 16px;
  color: #606266;
  margin-bottom: 20px;
  font-weight: 500;
}

/* 浮动按钮 */
.floating-button {
  position: fixed;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2000;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.floating-button:hover {
  transform: translateY(-50%) scale(1.05);
}

.floating-button:hover .button-text {
  opacity: 1;
  transform: translateX(0);
}

.button-icon {
  width: 60px;
  height: 60px;
  font-size: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  position: relative;
}

.button-icon:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.button-text {
  margin-top: 8px;
  color: #606266;
  font-size: 14px;
  opacity: 0;
  transform: translateX(10px);
  transition: all 0.3s ease;
  white-space: nowrap;
  background: rgba(255, 255, 255, 0.9);
  padding: 4px 8px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: #f56c6c;
  color: white;
  border-radius: 50%;
  min-width: 22px;
  height: 22px;
  line-height: 22px;
  font-size: 12px;
  text-align: center;
  padding: 0 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* 抽屉样式 */
.question-drawer {
  z-index: 2001 !important;
}

.question-drawer :deep(.el-drawer__body) {
  padding: 0;
  overflow: hidden;
}

.drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  border-bottom: 1px solid #f0f0f0;
}

.drawer-title {
  font-size: 18px;
  font-weight: 500;
}

.drawer-actions {
  display: flex;
  gap: 10px;
}

.selected-question {
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.selected-question .question-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.selected-question .question-index {
  position: relative;
  top: 0;
  left: 0;
  font-weight: 500;
  margin-right: 10px;
  font-size: 16px;
}

.selected-question .question-meta {
  flex: 1;
  display: flex;
  gap: 8px;
  align-items: center;
}

.selected-question .knowledge {
  color: #409eff;
  font-size: 14px;
}

.selected-question .question-image {
  width: 100%;
  max-height: 200px;
  object-fit: contain;
}

.empty-tip {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* 上传解析样式 */
.upload-analysis-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.analysis-uploader {
  width: 100%;
}

.mb-3 {
  margin-bottom: 12px;
}

.upload-area {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 200px;
  width: 450px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  transition: border-color 0.3s;
}

.upload-area:hover {
  border-color: #409eff;
}

.upload-icon {
  font-size: 28px;
  color: #8c939d;
  margin-bottom: 10px;
}

.upload-text {
  color: #606266;
  font-size: 14px;
  margin-bottom: 8px;
}

.upload-tip {
  color: #909399;
  font-size: 12px;
}

.preview-image {
  width: 100%;
  max-height: 200px;
  object-fit: contain;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.preview-actions {
  margin-top: 15px;
  display: flex;
  justify-content: center;
  gap: 15px;
}

.question-item {
  margin-bottom: 10px;
  border: 2px solid #ebeef5 !important;
  border-radius: 8px !important;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

/* 添加最后一个元素的样式 */
.question-list .question-item:last-child {
  margin-bottom: 0;
}

.question-item:hover {
  border-color: #d1e9ff !important;
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.meta-info {
  display: flex;
  align-items: center;
  gap: 5px;
  background: #f8fbff;
  border-radius: 6px 6px 0 0;
  justify-content: space-between;
  margin-bottom: 0;
  padding: 8px 15px;
  border-bottom: 1px solid #ebeef5;
}

.context-image {
  border-radius: 4px;
  border: none;
  display: flex;
  justify-content: left;
  float: none;
  align-items: flex-start;
  position: relative;
  max-width: 800px;
  min-width: 600px;
  margin: 15px auto;
  padding: 0 30px;
}

.question-source {
  font-size: 14px;
  color: #606266;
  margin-right: auto;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.source-info-item {
  background-color: #f5f6f6;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 14px;
}

.source-tag-item {
  color: #59bd07;
  background-color: #f5f6f6;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 14px;
}

.answer-control {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background-color: #fafafa;
  border-top: 1px dashed #ebeef5;
}

.custom-zoom-enter-active,
.custom-zoom-leave-active {
  transition: all 0.3s ease;
}

.custom-zoom-enter-from,
.custom-zoom-leave-to {
  opacity: 0;
  transform: scale(0.95);
}

/* 拖拽相关样式 */
.draggable-container {
  min-height: 10px;
}

.drag-icon {
  margin-right: 8px;
  padding: 4px;
  color: #909399;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s;
}

.selected-question {
  transition: all 0.3s ease;
  position: relative;
  background-color: #fff;
  border-left: 3px solid transparent;
  cursor: move; /* 显示拖动光标 */
  user-select: none; /* 防止文本选择 */
}

.selected-question:hover {
  background-color: #f8f9fa;
  border-left: 3px solid #409eff;
}

.selected-question:hover .drag-icon {
  color: #409eff;
  background-color: #f0f2f5;
}

.selected-question.sortable-chosen {
  background-color: #f0f7ff;
  box-shadow: 0 5px 15px 0 rgba(0, 0, 0, 0.15);
  z-index: 10;
  transform: scale(1.01);
  border-left: 3px solid #409eff;
}

.selected-question.sortable-ghost {
  opacity: 0.6;
  background-color: #e6f7ff;
  border: 2px dashed #409eff;
  border-radius: 8px;
}

/* 调整question-header布局 */
.question-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  gap: 5px;
}

/* loading样式优化 */
.question-list {
  min-height: 400px;
}

.el-loading-mask {
  background-color: rgba(255, 255, 255, 0.9) !important;
  z-index: 1000 !important;
}

.el-loading-spinner {
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  margin: 0 !important;
}

.el-loading-spinner .circular {
  width: 60px !important;
  height: 60px !important;
}

.el-loading-spinner .el-loading-text {
  color: #409EFF !important;
  font-size: 16px !important;
  margin-top: 15px !important;
  font-weight: bold !important;
}
</style>
