import request from '@/utils/request'

// 查询试卷列表
export function listPaper(query) {
  return request({
    url: '/qh/paper/list',
    method: 'get',
    params: query
  })
}

// 首页数据查询
export function dashboard(query) {
  return request({
    url: '/qh/paper/dashboard',
    method: 'get',
    params: query
  })
}

// 查询试卷
export function getPaper(id) {
  return request({
    url: '/qh/paper/' + id,
    method: 'get'
  })
}

// 查询试卷详细
export function getPaperInfo(id) {
  return request({
    url: '/qh/paper/info/' + id,
    method: 'get'
  })
}

// 新增试卷
export function addPaper(data) {
  return request({
    url: '/qh/paper',
    method: 'post',
    data: data
  })
}

// 修改试卷
export function updatePaper(data) {
  return request({
    url: '/qh/paper',
    method: 'put',
    data: data
  })
}

// 删除试卷
export function delPaper(id, flag) {
  return request({
    url: '/qh/paper/' + id,
    method: 'delete',
    params: {
      flag: flag
    }
  })
}

// 平行组卷
export function pingXingPaper(id) {
  return request({
    url: '/qh/paper/pingxing/' + id,
    method: 'get'
  })
}

// 导出试卷分析报告
export function exportPaperReport(id, fileType) {
  return request({
    url: '/qh/paper/export/report',
    method: 'post',
    params: {
      id: id,
      fileType: fileType
    },
    responseType: 'blob'
  })
}
