<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryRef" :inline="true" :model="queryParams">
      <el-form-item label="试卷名称" prop="paperName">
        <el-input
            v-model="queryParams.paperName"
            clearable
            placeholder="请输入试卷名称"
            style="width: 180px"
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="地区" prop="region">
        <el-cascader
          v-model="queryParams.regionList"
          :options="regionData"
          :props="{ value: 'value', label: 'label', children: 'children' }"
          clearable
          placeholder="请选择地区"
          style="width: 180px"
          @change="handleRegionChange"
        />
      </el-form-item>
        <el-form-item label="试卷类型" prop="paperType">
            <el-select v-model="queryParams.paperType" clearable placeholder="请选择试卷类型" style="width: 180px">
                <el-option
                        v-for="dict in sys_qh_paper_type"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                />
            </el-select>
        </el-form-item>
      <el-form-item label="组卷方式" prop="paperStyle">
        <el-select v-model="queryParams.paperStyle" clearable placeholder="请选择组卷方式" style="width: 180px">
          <el-option
              v-for="dict in sys_qh_zu_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="年份" prop="pyear">
        <el-date-picker
                v-model="queryParams.pyear"
                type="year"
                placeholder="选择年份"
                format="YYYY"
                value-format="YYYY"
                style="width: 180px"
        />
<!--        <el-input-->
<!--                v-model="queryParams.pyear"-->
<!--                clearable-->
<!--                placeholder="请输入年份"-->
<!--                style="width: 200px"-->
<!--                @keyup.enter="handleQuery"-->
<!--        />-->
      </el-form-item>
      <el-form-item>
        <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--            v-hasPermi="['qh:bank:export']"-->
<!--            icon="Download"-->
<!--            plain-->
<!--            type="warning"-->
<!--            @click="handleExport"-->
<!--        >导出-->
<!--        </el-button>-->
<!--      </el-col>-->
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="paperList" border @selection-change="handleSelectionChange">
<!--      <el-table-column align="center" type="selection" width="55"/>-->
      <el-table-column align="center" label="试卷名称" prop="paperName" width="320"/>
      <el-table-column align="center" label="试卷类型" prop="paperType" width="100">
        <template #default="scope">
          <dict-tag :options="sys_qh_paper_type" :value="scope.row.paperType"/>
        </template>
      </el-table-column>
      <el-table-column align="center" label="组卷方式" prop="paperStyle" width="100">
        <template #default="scope">
          <dict-tag :options="sys_qh_zu_type" :value="scope.row.paperStyle"/>
        </template>
      </el-table-column>
      <el-table-column align="center" label="题目数量" prop="num" width="80"/>
      <el-table-column align="center" label="总分" prop="score" width="80"/>
      <el-table-column align="center" label="地区" prop="region" width="200">
        <template #default="scope">
          {{ formatRegion(scope.row.region) }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="年份" prop="pyear"/>
      <el-table-column align="center" label="创建人" prop="createBy"/>
      <el-table-column align="center" label="创建时间" prop="createTime"/>
      <el-table-column align="center" label="最后更新时间" prop="updateTime"/>
      <el-table-column align="center" class-name="small-padding fixed-width" label="操作" width="180">
        <template #default="scope">
          <el-tooltip content="查看" placement="top">
            <el-button v-hasPermi="['qh:bank:query']" icon="View" link type="primary"
                       @click="handleView(scope.row)"></el-button>
          </el-tooltip>
          <el-tooltip content="修改" placement="top">
            <el-button v-hasPermi="['qh:bank:edit']" icon="Edit" link type="primary"
                       @click="handleUpdate(scope.row)"></el-button>
          </el-tooltip>
          <el-tooltip content="删除" placement="top">
            <el-button v-hasPermi="['qh:bank:remove']" icon="Delete" link type="danger"
                       @click="handleDelete(scope.row)"></el-button>
          </el-tooltip>
          <el-tooltip content="导出" placement="top">
            <el-button icon="Download" link type="warning" @click="handleExport(scope.row)"></el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total > 0"
        v-model:limit="queryParams.pageSize"
        v-model:page="queryParams.pageNum"
        :total="total"
        @pagination="getList"
    />

    <!-- 添加或修改定时任务对话框 -->
    <el-dialog v-model="open" :title="title" append-to-body width="820px">
      <el-form ref="paperRef" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="试卷名称" prop="paperName">
              <el-input v-model="form.paperName" placeholder="请输入试卷名称"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="试卷类型" prop="paperType">
              <el-select v-model="form.paperType" placeholder="请选择试卷类型">
                <el-option
                        v-for="dict in sys_qh_paper_type"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="地区" prop="region">
              <el-cascader
                v-model="form.regionList"
                :options="regionData"
                :props="{ value: 'value', label: 'label', children: 'children' }"
                clearable
                placeholder="请选择地区"
                style="width: 100%"
                @change="handleFormRegionChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="年份" prop="pyear">
              <el-date-picker
                      v-model="form.pyear"
                      type="year"
                      placeholder="选择年份"
                      format="YYYY"
                      value-format="YYYY"
                      style="width: 100%;"
              />
<!--              <el-input v-model="form.pyear" placeholder="请输入年份"/>-->
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 试卷查看抽屉 -->
    <el-drawer
            v-model="drawerVisible"
            :close-on-click-modal="true"
            :destroy-on-close="false"
            :with-header="true"
            direction="rtl"
            size="53%"
    >
      <template #header>
        <div class="drawer-header">
          <span class="drawer-title">查看试卷</span>
        </div>
      </template>
      <div class="drawer-content">
        <paper-detail-component
                v-if="selectedPaperId"
                :paper-id="selectedPaperId"
                :paper-name="currentPaperName"
                @close="closeDrawer"
        />
      </div>
    </el-drawer>
  </div>
</template>

<script name="Bank" setup>
import {listPaper, getPaper, delPaper, addPaper, updatePaper } from "@/api/qh/paper";
import { getCurrentInstance, reactive, ref, toRefs } from 'vue';
import { useRouter } from 'vue-router';
import PaperDetailComponent from '../test/components/PaperDetail.vue';
import { codeToText, regionData } from "element-china-area-data";

const router = useRouter();
const {proxy} = getCurrentInstance();
const {sys_qh_paper_type, sys_qh_zu_type} = proxy.useDict("sys_qh_paper_type", "sys_qh_zu_type");

const paperList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const openView = ref(false);
const openCron = ref(false);
const expression = ref("");

// 抽屉相关状态
const drawerVisible = ref(false);
const selectedPaperId = ref(null);
const currentPaperName = ref('');

const data = reactive({
  form: {
    paperName: '',
    paperType: '',
    region: '',
    regionList: [], // 新增级联选择器数据
    pyear: ''
  },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    paperName: '',
    paperType: '',
    paperStyle: '',
    region: '',
    regionList: [], // 新增级联选择器数据
    pyear: '',
    flag: 'ZJ'
  },
  rules: {
    // paperName: [{required: true, message: "试卷名称不能为空", trigger: "blur"}],
  }
});

const {queryParams, form, rules} = toRefs(data);

// 地区格式化方法，兼容数组和字符串格式
const formatRegion = (regionData) => {
  // 处理空值情况
  if (!regionData) {
    return '';
  }

  // 统一将输入转换为数组（确保至少3个元素）
  let regionCodes = [];
  if (Array.isArray(regionData)) {
    // 数组格式：截取前3个元素（确保是省、市、区三级）
    regionCodes = regionData.slice(0, 3);
  } else if (typeof regionData === 'string') {
    // 字符串格式：用逗号分割后取前3个元素
    regionCodes = regionData.split(',').map(code => code.trim()).filter(code => code).slice(0, 3);
  }

  // 确保有3个编码（不足则补空，避免数组长度不够）
  while (regionCodes.length < 3) {
    regionCodes.push('');
  }

  // 解析三级编码（省、市、区）
  const province = codeToText[regionCodes[0]] || ''; // 第一级：省
  const city = codeToText[regionCodes[1]] || '';     // 第二级：市
  const district = codeToText[regionCodes[2]] || ''; // 第三级：区

  // 处理特殊情况：如果市编码无效，但省编码有效（如直辖市）
  const validCity = city || province;
  // 拼接结果（过滤空值）
  const regionNames = [province, validCity, district].filter(name => name);

  // 特殊处理：如果只有一个有效名称（如直辖市）
  if (regionNames.length === 1) {
    return regionNames[0];
  }

  // 正常拼接为"省/市/区"
  return regionNames.join(' / ');
};

/** 查询试卷列表 */
function getList() {
  loading.value = true;
  listPaper(queryParams.value).then(response => {
    paperList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  }).catch(error => {
    loading.value = false;
  });
}

/** 任务组名字典翻译 */
// function jobGroupFormat(row, column) {
//   return proxy.selectDictLabel(sys_qh_zu_type.value, row.paperStyle);
// }

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    id: '',
    paperName: '',
    paperType: '',
    paperStyle: '',
    region: '',
    regionList: [], // 重置级联选择器数据
    pyear: '',
  };
  proxy.resetForm("paperRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  // 保存当前flag值
  const currentFlag = queryParams.value.flag;
  proxy.resetForm("queryRef");
  // 恢复flag值
  queryParams.value.flag = currentFlag;
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const id = row.id || ids.value;
  getPaper(id).then(response => {
    // 处理地区数据格式转换
    form.value = handlePaperData(response.data);
    open.value = true;
    title.value = "修改试卷";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["paperRef"].validate(valid => {
    if (valid) {
      if (form.value.id !== '') {
        updatePaper(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addPaper(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const id = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除【' + row.paperName + '】?').then(function () {
    return delPaper(id, 'ZJ');
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {
  });
}

/** 导出按钮操作 */
function handleExport(row) {
  // 如果传入了行数据，则导出特定试卷，否则导出所有符合条件的试卷
  if (row && row.id) {
    proxy.download("qh/paper/export", {
      id: row.id
    }, `paper_${row.paperName}_${new Date().getTime()}.xlsx`);
  } else {
    proxy.download("qh/paper/export", {
      ...queryParams.value,
    }, `paper_${new Date().getTime()}.xlsx`);
  }
}

/** 查看按钮操作 */
function handleView(row) {
  selectedPaperId.value = row.id;
  currentPaperName.value = row.paperName;
  drawerVisible.value = true;
}

/** 关闭抽屉 */
function closeDrawer() {
  drawerVisible.value = false;
  // 重置selectedPaperId，确保下次打开抽屉时会重新触发组件挂载
  selectedPaperId.value = null;
}

/** 处理查询表单地区变更 */
function handleRegionChange(value) {
  if (value && value.length > 0) {
    // 将级联选择器的值转换为逗号分隔的字符串，用于后端查询
    queryParams.value.region = value.join(',');
  } else {
    queryParams.value.region = '';
  }
}

/** 处理编辑表单地区变更 */
function handleFormRegionChange(value) {
  if (value && value.length > 0) {
    // 将级联选择器的值转换为逗号分隔的字符串，用于保存
    form.value.region = value.join(',');
  } else {
    form.value.region = '';
  }
}

/** 处理获取到的试卷数据，转换地区格式 */
function handlePaperData(paperData) {
  if (paperData && paperData.region) {
    // 将逗号分隔的地区字符串转换为数组，用于级联选择器显示
    const regionCodes = paperData.region.split(',').filter(code => code.trim());
    if (regionCodes.length > 0) {
      paperData.regionList = regionCodes;
    }
  }
  return paperData;
}

getList();
</script>

<style scoped>
.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.drawer-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.drawer-content {
  padding: 10px;
  height: 100%;
}
</style>
