{"name": "domino", "version": "3.8.8", "description": "domino管理系统", "author": "domino", "license": "MIT", "type": "module", "scripts": {"dev": "vite", "build:prod": "vite build", "build:stage": "vite build --mode staging", "preview": "vite preview"}, "repository": {"type": "git", "url": "https://gitee.com/lostaya/dpp-system-vue.git"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "10.11.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "0.28.1", "echarts": "5.5.1", "element-china-area-data": "^6.1.0", "element-plus": "2.7.6", "file-saver": "2.0.5", "fuse.js": "6.6.2", "html2canvas": "^1.4.1", "js-cookie": "3.0.5", "jsencrypt": "3.3.2", "jspdf": "^3.0.1", "nprogress": "0.2.0", "pinia": "2.1.7", "vue": "3.4.31", "vue-cropper": "^1.1.4", "vue-router": "4.4.0", "vuedraggable": "^4.1.0"}, "devDependencies": {"@rollup/plugin-commonjs": "^28.0.3", "@vitejs/plugin-vue": "5.0.5", "sass": "1.77.5", "unplugin-auto-import": "0.17.6", "unplugin-vue-setup-extend-plus": "1.0.1", "vite": "5.3.2", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "2.0.1"}}