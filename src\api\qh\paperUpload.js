import request from '@/utils/request'

// 上传试卷进行解析
export function uploadPaperFile(file) {
  // 创建FormData对象，用于上传文件
  const formData = new FormData()
  formData.append('file', file)
  return request({
    url: 'qh/upload/uploadPaper',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 获取历史解析记录
export function uploadStatus() {
  return request({
    url: '/qh/upload/status',
    method: 'get'
  })
}

// 获取文件解析记录
export function uploadResult(fileName) {
  return request({
    url: '/qh/upload/result',
    method: 'get',
    params: { fileName } // 直接将fileName作为参数名
  })
}
