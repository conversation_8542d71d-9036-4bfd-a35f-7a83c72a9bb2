<template>
  <div class="paper-detail-container">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton animated :rows="10" />
    </div>

    <div v-else>
      <!-- 试卷标题 -->
      <div class="paper-title">
        <h2>{{ paperName || '试卷详情' }}</h2>
      </div>

      <!-- 试卷信息 -->
      <div class="paper-info">
        <span>总分：{{ totalScore }}分</span>
        <span class="paper-info-item">题目数量：{{ paperData.length }}道</span>
      </div>

      <el-divider></el-divider>

      <!-- 试卷内容 -->
      <div class="paper-content">
        <!-- 动态生成各种题型部分 -->
        <div v-for="(questions, type) in questionGroups" :key="type" class="question-section">
          <div class="section-title">{{ getQuestionTypeName(type) }}</div>
          <div class="question-list">
            <div v-for="(question, index) in questions" :key="question.id" class="question-item">
              <div class="question-header">
                <span class="question-num">{{ getQuestionNumber(type, index) }}.</span>
                <div class="question-content">
                  <img :src="question.context" class="question-image"/>
                </div>
              </div>
              <div class="question-meta">
                <span v-if="question.sourcePaper">来源：{{ question.sourcePaper }}</span>
                <span v-if="question.region" class="paper-info-item">地区：{{ question.region }}</span>
                <span v-if="question.year" class="paper-info-item">年份：{{ question.year }}</span>
                <span class="paper-info-item">分值：{{ question.score }}分</span>
                <span class="paper-info-item">难度：{{ getDifficultyLabel(question.difficulty) }}</span>
                <el-button class="analysis-btn" icon="View" plain size="small" type="primary"
                           @click="toggleAnalysis(question.id)">
                  {{ showAnalysisId === question.id ? '收起解析' : '展开解析' }}
                </el-button>
              </div>
              <div v-if="showAnalysisId === question.id" class="question-analysis">
                <div class="analysis-content">
                  <img :src="question.questionAnalyze" class="analysis-image"/>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {computed, getCurrentInstance, onMounted, ref, watch} from 'vue';
import {getPaperInfo} from '@/api/qh/paper';

const {proxy} = getCurrentInstance();
// 使用系统字典
const {sys_qh_questions_type, sys_qh_difficulty} = proxy.useDict("sys_qh_questions_type", "sys_qh_difficulty");

const props = defineProps({
  paperId: {
    type: [Number, String],
    required: true
  },
  paperName: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['close']);

// 控制解析显示
const showAnalysisId = ref(null);

// 试卷数据
const paperData = ref([]);

// 加载状态
const loading = ref(false);

// 计算总分
const totalScore = computed(() => {
  return paperData.value.reduce((total, item) => {
    return total + Number(item.score || 0);
  }, 0);
});

// 按题型分组题目
const questionGroups = computed(() => {
  const groups = {};
  paperData.value.forEach(question => {
    const type = question.questionType;
    if (!groups[type]) {
      groups[type] = [];
    }
    groups[type].push(question);
  });

  // 按题型排序
  return Object.keys(groups).sort().reduce((obj, key) => {
    obj[key] = groups[key];
    return obj;
  }, {});
});

// 获取问题类型名称
const getQuestionTypeName = (type) => {
  // 从系统字典中查找匹配的题型
  const typeItem = sys_qh_questions_type.value.find(item => item.value === type);
  return typeItem ? typeItem.label : `题型${type}`;
};

// 获取难度标签
const getDifficultyLabel = (difficulty) => {
  const diffItem = sys_qh_difficulty.value.find(item => item.value === difficulty);
  return diffItem ? diffItem.label : '未知难度';
};

// 生成题号
const getQuestionNumber = (type, index) => {
  let startNumber = 1;

  // 查找在当前题型之前的所有题目数量
  const types = Object.keys(questionGroups.value).sort();
  const currentTypeIndex = types.indexOf(type);

  for (let i = 0; i < currentTypeIndex; i++) {
    startNumber += questionGroups.value[types[i]].length;
  }

  return startNumber + index;
};

// 切换解析显示状态
const toggleAnalysis = (id) => {
  if (showAnalysisId.value === id) {
    showAnalysisId.value = null;
  } else {
    showAnalysisId.value = id;
  }
};

// 获取试卷详情
const getPaperDetail = async (id) => {
  // 设置加载状态为 true
  loading.value = true;
  // 重置数据
  paperData.value = [];

  try {
    const res = await getPaperInfo(id);
    if (res.code === 200) {
      paperData.value = res.data || [];
    } else {
      // 接口返回错误，确保数据为空
      paperData.value = [];
    }
  } catch (error) {
    console.error('获取试卷详情失败', error);
    // 发生错误时清空数据
    paperData.value = [];
  } finally {
    // 无论成功还是失败，都将加载状态设置为 false
    loading.value = false;
  }
};

// 监听 paperId 变化，重新获取数据
watch(() => props.paperId, (newId, oldId) => {
  if (newId && newId !== oldId) {
    getPaperDetail(newId);
  }
}, { immediate: true });

onMounted(() => {
  if (props.paperId) {
    getPaperDetail(props.paperId);
  }
});
</script>

<style scoped>
.paper-detail-container {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

.loading-container {
  padding: 20px;
}

.paper-title {
  text-align: center;
  margin-bottom: 10px;
}

.paper-title h2 {
  font-size: 22px;
  font-weight: bold;
  color: #333;
  margin: 0;
}

.paper-info {
  text-align: center;
  color: #666;
  font-size: 14px;
  margin-bottom: 20px;
}

.paper-info-item {
  margin-left: 15px;
}

.paper-content {
  margin-top: 20px;
}

.question-section {
  margin-bottom: 30px;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
  padding-left: 10px;
  border-left: 4px solid #409eff;
}

.question-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.question-item {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 15px;
  background-color: #fff;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.05);
}

.question-header {
  display: flex;
  margin-bottom: 15px;
}

.question-num {
  font-weight: bold;
  margin-right: 10px;
  flex-shrink: 0;
}

.question-content {
  flex: 1;
}

.question-image, .analysis-image {
  max-width: 100%;
  border-radius: 4px;
  pointer-events: none; /* 禁用所有鼠标事件 */
  user-select: none; /* 禁止选择 */
  display: block;
}

.question-meta {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  font-size: 13px;
  color: #909399;
  margin-top: 15px;
}

.question-meta span {
  margin-right: 15px;
  margin-bottom: 5px;
}

.analysis-btn {
  margin-left: auto;
}

.question-analysis {
  margin-top: 15px;
  padding: 15px;
  background-color: #f8f8f8;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}

.analysis-content {
  font-size: 14px;
  color: #606266;
  line-height: 1.6;
}
</style>
