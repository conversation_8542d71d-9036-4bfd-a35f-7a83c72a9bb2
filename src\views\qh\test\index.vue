<template>
  <div class="app-container">
    <!-- 头部标题和操作区 -->
    <div class="header-bar">
      <h3 class="title">试卷列表</h3>
      <div class="actions">
        <el-button size="small" type="primary" @click="handleManualPaper">手动组卷</el-button>
        <el-button size="small" type="success" @click="handleAutoPaper">自动组卷</el-button>
      </div>
    </div>

    <!-- 试卷列表页 -->
    <div v-loading="pageLoading">
      <el-row :gutter="24">
        <!-- 左侧试卷目录树 -->
        <!-- <el-col :span="5" :xs="24">
          <div class="left-container">
            <div class="category-search">
              <el-input
                      v-model="filterText"
                      clearable
                      placeholder="输入关键词"
                      prefix-icon="Search"
              />
            </div>
            <div class="category-tree">
              <el-tree
                      ref="paperCategoryRef"
                      :data="categoryTreeData"
                      :expand-on-click-node="false"
                      :filter-node-method="filterNode"
                      :props="{ label: 'name', children: 'children' }"
                      class="paper-category-tree"
                      highlight-current
                      node-key="id"
                      @node-click="handleNodeClick"
              >
                <template #default="{ node, data }">
                  <span class="tree-node-content">
                    <span class="node-label">{{ data.name }}</span>
                    <span class="edit-link" v-if="data.name === '我的试卷'" @click.stop="openCategoryManageDialog">编辑</span>
                  </span>
                </template>
              </el-tree>
            </div>
          </div>
        </el-col> -->

        <!-- 右侧试卷列表 -->
        <el-col :span="24" :xs="24">
          <div class="right-container">
            <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="80px" size="default">
              <el-form-item label="试卷名称" prop="paperName">
                <el-input
                        v-model="queryParams.paperName"
                        clearable
                        style="width: 200px"
                        placeholder="请输入试卷名称"
                        @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item label="题库来源" prop="sourcePaper">
                <el-input
                        v-model="queryParams.sourcePaper"
                        clearable
                        style="width: 200px"
                        placeholder="请输入题库来源"
                        @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item>
                <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>

            <!--            <el-row :gutter="10" class="mb8">-->
            <!--              <el-col :span="1.5">-->
            <!--                <el-button-->
            <!--                        icon="Download"-->
            <!--                        plain-->
            <!--                        size="defalt"-->
            <!--                        type="warning"-->
            <!--                        @click="handleExport"-->
            <!--                >导出-->
            <!--                </el-button>-->
            <!--              </el-col>-->
            <!--              <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>-->
            <!--            </el-row>-->

            <!-- 表格容器 -->
            <div class="table-container">
              <el-table
                      v-loading="loading"
                      :data="paperList"
                      border
                      height="440"
                      :max-height="tableMaxHeight"
                      @selection-change="handleSelectionChange">
                <!--              <el-table-column align="center" type="selection" width="55"/>-->
                <el-table-column align="center" label="试卷名称" prop="paperName" width="320"/>
                <el-table-column :show-overflow-tooltip="true" align="center" label="题库来源" prop="sourcePaper"/>
                <el-table-column align="center" label="创建人" prop="createBy" width="100"/>
                <el-table-column align="center" label="创建时间" prop="createTime" width="130"/>
                <el-table-column align="center" label="最后更新时间" prop="updateTime" width="130"/>
                <el-table-column align="center" class-name="small-padding fixed-width" label="操作" width="150" fixed="right">
                  <template #default="scope">
                    <el-tooltip content="查看" placement="top">
                      <el-button icon="View" link type="primary" @click="handleView(scope.row)"></el-button>
                    </el-tooltip>
                    <!--                    <el-tooltip content="修改" placement="top">-->
                    <!--                      <el-button icon="Edit" link type="primary" @click="handleUpdate(scope.row)"></el-button>-->
                    <!--                    </el-tooltip>-->
                    <el-tooltip content="删除" placement="top">
                      <el-button icon="Delete" link type="danger" @click="handleDelete(scope.row)"></el-button>
                    </el-tooltip>
                    <el-tooltip content="平行组卷" placement="top">
                      <el-button icon="Operation" link type="primary" @click="handlePingXing(scope.row)"></el-button>
                    </el-tooltip>
                    <el-tooltip content="导出" placement="top">
                      <el-button icon="Download" link type="warning" @click="handleExport(scope.row)"></el-button>
                    </el-tooltip>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <pagination
                    v-show="total>0"
                    v-model:limit="queryParams.pageSize"
                    v-model:page="queryParams.pageNum"
                    :total="total"
                    @pagination="getList"
            />
          </div>
        </el-col>
      </el-row>

      <!-- 添加或修改试卷对话框 -->
      <el-dialog v-model="open" :title="title" append-to-body width="500px">
        <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
          <el-form-item label="试卷名称" prop="paperName">
            <el-input v-model="form.paperName" placeholder="请输入试卷名称"/>
          </el-form-item>
          <!-- <el-form-item label="试卷类型" prop="paperType">
            <el-select v-model="form.paperType" placeholder="请选择试卷类型" style="width: 100%">
              <el-option
                v-for="dict in sys_qh_paper_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="地区">
            <el-cascader
              v-model="form.region"
              :options="regionData"
              :props="{ value: 'value', label: 'label', children: 'children' }"
              clearable
              placeholder="请选择地区"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="年份">
            <el-date-picker
              v-model="form.year"
              type="year"
              placeholder="请选择年份"
              value-format="YYYY"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="组卷目录" prop="folderId">
            <el-input
              v-model="form.folderName"
              placeholder="请选择组卷目录"
              readonly
            >
              <template #append>
                <el-button icon="FolderOpened" />
              </template>
            </el-input>
          </el-form-item> -->
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </template>
      </el-dialog>

      <!-- 目录管理弹窗 -->
      <!-- <el-dialog v-model="categoryManageOpen" :title="categoryManageTitle" append-to-body width="700px" @close="closeCategoryManageDialog">
        <div style="display: flex; gap: 20px;">
          <el-tree
                  ref="manageTreeRef"
                  :data="manageTreeData"
                  :props="{ label: 'name', children: 'children' }"
                  node-key="id"
                  highlight-current
                  @node-click="handleManageNodeClick"
                  style="width: 250px; border: 1px solid #eee; border-radius: 4px; padding: 10px;"
          />
          <div style="flex: 1;">
            <el-form ref="categoryFormRef" :model="categoryForm" :rules="categoryRules" label-width="100px">
              <el-form-item label="目录名称" prop="name">
                <el-input v-model="categoryForm.name" placeholder="请输入目录名称" />
              </el-form-item>
              <el-form-item label="上级目录" prop="parentName">
                <el-input v-model="categoryForm.parentName" disabled />
              </el-form-item>
              <el-form-item label="显示顺序" prop="orderNum">
                <el-input-number v-model="categoryForm.orderNum" :min="0" />
              </el-form-item>
            </el-form>
            <div style="margin-top: 20px; display: flex; justify-content: flex-end; gap: 10px;">
              <el-button type="primary" @click="submitCategoryForm">保存</el-button>
              <el-button @click="resetCategory">重置</el-button>
              <el-button type="danger" @click="handleDeleteCategory">删除</el-button>
            </div>
          </div>
        </div>
      </el-dialog> -->

      <!-- 导出报告对话框 -->
      <el-dialog v-model="exportOpen" title="导出分析报告" append-to-body width="400px">
        <el-form ref="exportFormRef" :model="exportForm" label-width="100px">
          <el-form-item label="文件格式" prop="fileType">
            <el-radio-group v-model="exportForm.fileType">
              <el-radio label="pdf">PDF格式</el-radio>
              <el-radio label="excel">Excel格式</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="confirmExport">确 定</el-button>
            <el-button @click="exportOpen = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
    </div>

    <!-- 试卷查看抽屉 -->
    <el-drawer
            v-model="drawerVisible"
            :close-on-click-modal="true"
            :destroy-on-close="false"
            :with-header="true"
            direction="rtl"
            size="53%"
    >
      <template #header>
        <div class="drawer-header">
          <span class="drawer-title">查看试卷</span>
        </div>
      </template>
      <div class="drawer-content">
        <paper-detail-component
                v-if="selectedPaperId"
                :paper-id="selectedPaperId"
                :paper-name="currentPaperName"
                @close="closeDrawer"
        />
      </div>
    </el-drawer>
  </div>
</template>

<script name="TestPaper" setup>
  import {onMounted, reactive, ref, getCurrentInstance, nextTick, onBeforeUnmount} from 'vue';
  import {useRouter} from 'vue-router';
  import {delPaper, listPaper, updatePaper, pingXingPaper, exportPaperReport, getPaper} from "@/api/qh/paper";
  import {ElMessage, ElMessageBox, ElLoading} from 'element-plus';
  import PaperDetailComponent from './components/PaperDetail.vue';
  import { saveAs } from 'file-saver';
  // import { selectKnowledgeTreeList, addKnowledgeTree, updateKnowledgeTree, delKnowledgeTree } from '@/api/qh/knowledgeTree';
  // import { handleTree } from '@/utils/domino';
  // import { regionData } from 'element-china-area-data';

  const router = useRouter();
  const {proxy} = getCurrentInstance();
  // const { sys_qh_paper_type } = proxy.useDict('sys_qh_paper_type');
  // 遮罩层
  const loading = ref(true);
  // 页面初始加载
  const pageLoading = ref(true);
  // 选中数组
  const ids = ref([]);
  // 非单个禁用
  const single = ref(true);
  // 非多个禁用
  const multiple = ref(true);
  // 显示搜索条件
  const showSearch = ref(true);
  // 总条数
  const total = ref(0);
  // 试卷表格数据
  const paperList = ref([]);
  // 弹出层标题
  const title = ref("");
  // 是否显示弹出层
  const open = ref(false);
  //目录弹出层标题
  // const categoryTitle = ref('');
  // 是否显示目录弹出层
  // const categoryOpen = ref(false);
  // 是否显示自动组卷
  // const autoPaperOpen = ref(false);
  // 导出对话框
  let exportOpen = ref(false);
  // 导出表单
  const exportForm = reactive({
    fileType: 'pdf',
    paperId: ''
  });
  // 查询参数
  const queryParams = reactive({
    pageNum: 1,
    pageSize: 10,
    paperName: '',
    paperType: '',
    sourcePaper: '',
    // folder: '', // 目录ID，顶层目录时为空
    flag: 'SJ',
  });
  // 表单参数
  const form = reactive({
    id: '',
    paperName: '',
    // paperType: '',
    // region: [],
    // year: '',
    // folderId: '',
    // folderName: '',
    sourcePaper: ''
  });
  // 表单校验
  const rules = {
    paperName: [
      {required: true, message: "试卷名称不能为空", trigger: "blur"}
    ],
    // paperType: [
    //   {required: true, message: "试卷类型不能为空", trigger: "change"}
    // ],
    // folderId: [
    //   {required: true, message: "组卷目录不能为空", trigger: "change"}
    // ],
  };
  // 目录表单参数（与后端实体类字段一致）
  // const categoryForm = reactive({
  //   id: '',
  //   parentId: '',
  //   name: '',
  //   parentName: '',
  //   orderNum: 0,
  //   nodeType: '6',
  //   status: '0',
  //   remark: ''
  // });
  // 目录表单校验
  // const categoryRules = {
  //   name: [
  //     {required: true, message: "目录名称不能为空", trigger: "blur"}
  //   ],
  // };
  // 目录树数据
  // const categoryTreeData = ref([]);
  // 试题篮Id
  const selectedPaperId = ref(null);
  // 树搜索过滤文本
  // const filterText = ref('');
  // 抽屉可见性
  const drawerVisible = ref(false);
  // 当前试卷名称
  const currentPaperName = ref('');
  // 是否显示目录管理弹窗
  // const categoryManageOpen = ref(false);
  // 目录管理弹窗标题
  // const categoryManageTitle = ref('试卷目录管理');
  // 目录管理弹窗内的树数据
  // const manageTreeData = ref([]);
  // 目录管理弹窗内的当前选中节点
  // const manageCurrentNode = ref(null);

  // 表格最大高度控制
  const tableMaxHeight = ref(500);

  // 重新计算表格高度的函数
  const calcTableHeight = () => {
    // 根据屏幕高度动态计算表格高度，留出足够空间给分页组件和其他元素
    const windowHeight = window.innerHeight;
    // 预留空间：顶部标题栏、搜索栏、分页组件、边距等
    tableMaxHeight.value = windowHeight - 280;

    // 设置最小高度，避免在小屏幕上表格太小
    if (tableMaxHeight.value < 300) {
      tableMaxHeight.value = 300;
    }
  };

  // 窗口大小变化时重新计算表格高度
  const handleResize = () => {
    calcTableHeight();
  };

  // 监听过滤文本变化
  // watch(filterText, (val) => {
  //   proxy.$refs.paperCategoryRef.filter(val);
  // });

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  function resetQuery() {
    // 保存当前flag值
    const currentFlag = queryParams.flag;

    proxy.resetForm("queryForm");

    // 恢复flag值
    queryParams.flag = currentFlag;

    handleQuery();
  }

  /** 查询试卷列表 */
  function getList() {
    loading.value = true;
    listPaper(queryParams).then(response => {
      paperList.value = response.rows;
      total.value = response.total;
    }).finally(() => {
      loading.value = false;
      pageLoading.value = false;
    });
  }

  /** 通过条件过滤节点 */
  // const filterNode = (value, data) => {
  //   if (!value) return true;
  //   return data.name && data.name.toLowerCase().indexOf(value.toLowerCase()) !== -1;
  // };

  /** 处理节点点击事件 */
  // function handleNodeClick(data) {
  //   // 判断是否是顶层目录
  //   const isTopFolder = data.parentId === '0';
  //
  //   // 如果是顶层目录，folder传空；非顶层目录，folder传值
  //   queryParams.folder = isTopFolder ? '' : data.id;
  //
  //   // 清空其他搜索条件
  //   queryParams.paperName = '';
  //   queryParams.sourcePaper = '';
  //
  //   // 触发查询
  //   handleQuery();
  // }

  /** 打开抽屉查看试卷 */
  function handleView(row) {
    selectedPaperId.value = row.id;
    currentPaperName.value = row.paperName;
    drawerVisible.value = true;
  }

  /** 关闭抽屉 */
  function closeDrawer() {
    drawerVisible.value = false;
    // 重置selectedPaperId，确保下次打开抽屉时会重新触发组件挂载
    selectedPaperId.value = null;
  }

  /** 多选框选中数据 */
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length !== 1;
    multiple.value = !selection.length;
  }

  /** 开启自动组卷 */
  function handleAutoPaper() {
    router.push('/qh/paper/index');
  }

  /** 开启手动组卷 */
  function handleManualPaper() {
    router.push('/qh/questionBank');
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const _ids = row.id || ids.value;
    ElMessageBox.confirm('是否确认删除【"' + row.paperName + '"】?', "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }).then(function () {
      return delPaper(_ids, 'SJ');
    }).then(() => {
      getList();
      ElMessage.success("删除成功");
    }).catch(() => {
    });
  }

  /** 导出按钮操作 */
  function handleExport(row) {
    // if (multiple.value) {
    //   ElMessage.warning("请选择一个试卷进行导出");
    //   return;
    // }

    exportForm.paperId = row.id;
    exportOpen.value = true;
  }

  /** 确认导出 */
  function confirmExport() {
    const loading = ElLoading.service({
      lock: true,
      text: '正在导出数据，请稍候...',
      background: 'rgba(0, 0, 0, 0.7)'
    });

    exportPaperReport(exportForm.paperId, exportForm.fileType).then(response => {
      loading.close();
      // 检查响应类型，确保是二进制数据
      const isBlob = response instanceof Blob;
      if (!isBlob) {
        ElMessage.error("导出失败：服务器未返回有效数据");
        return;
      }
      // 检查是否是错误消息（文本格式）
      if (response.type === 'text/html' || response.type === 'text/plain') {
        // 读取错误消息并显示
        const reader = new FileReader();
        reader.onload = () => {
          const errorMsg = reader.result;
          ElMessage.error(errorMsg || "导出失败");
        };
        reader.readAsText(response);
        return;
      }
      // 正常处理PDF或Excel文件
      const fileType = exportForm.fileType;
      const contentType = fileType === 'pdf' ? 'application/pdf' : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      // 创建正确的Blob对象，确保设置正确的MIME类型
      const blob = new Blob([response], { type: contentType });
      // 获取试卷名称用作文件名
      getPaper(exportForm.paperId).then(res => {
        if (res.code === 200 && res.data) {
          const fileName = res.data.paperName || `试卷分析报告_${new Date().getTime()}`;
          const fileExtension = fileType === 'pdf' ? '.pdf' : '.xlsx';
          saveAs(blob, `${fileName}${fileExtension}`);
          exportOpen.value = false;
          ElMessage.success("导出成功");
        } else {
          // 如果获取试卷名称失败，使用默认名称
          const defaultFileName = `试卷分析报告_${new Date().getTime()}.${fileType}`;
          saveAs(blob, defaultFileName);
          exportOpen.value = false;
          ElMessage.success("导出成功");
        }
      }).catch(() => {
        // 如果API调用失败，使用默认名称
        const defaultFileName = `试卷分析报告_${new Date().getTime()}.${fileType}`;
        saveAs(blob, defaultFileName);
        exportOpen.value = false;
        ElMessage.success("导出成功");
      });
    }).catch(error => {
      loading.close();
      ElMessage.error(error.message || "导出失败，请联系管理员");
    });
  }

  /** 平行组卷操作 */
  function handlePingXing(row) {
    ElMessageBox.confirm('确定要基于此试卷进行平行组卷？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    }).then(() => {
      const loading = ElLoading.service({
        lock: true,
        text: '平行组卷中，请稍候...',
        background: 'rgba(0, 0, 0, 0.7)'
      });

      pingXingPaper(row.id).then(response => {
        loading.close();
        if (response.code === 200) {
          ElMessage.success('平行组卷成功');
          getList();
        } else {
          ElMessage.error(response.msg || '平行组卷失败');
        }
      }).catch(error => {
        loading.close();
      });
    }).catch(() => {});
  }

  /** 新增按钮操作 */
  function handleAdd() {
    // 新增逻辑
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    title.value = "修改试卷";
    open.value = true;
    // 复制行数据到表单对象
    Object.assign(form, row);
  }

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs["formRef"].validate(valid => {
      if (valid) {
        if (form.id) {
          // 如果有ID，则是更新操作
          updatePaper(form).then(response => {
            if (response.code === 200) {
              ElMessage.success("修改成功");
              open.value = false;
              getList();
            } else {
              ElMessage.error(response.msg);
            }
          });
        }
      }
    });
  }

  /** 取消按钮 */
  function cancel() {
    open.value = false;
    reset();
  }

  /** 重置数据表单 */
  function reset() {
    Object.keys(form).forEach(key => {
      form[key] = undefined;
    });
    proxy.resetForm("formRef");
  }

  // 取消目录按钮
  // function cancelCategory() {
  //   categoryOpen.value = false;
  //   resetCategory();
  // }

  // 重置目录表单
  // function resetCategory() {
  //   // 保存当前父级信息
  //   const currentParentId = categoryForm.parentId;
  //   const currentParentName = categoryForm.parentName;
  //
  //   // 重置其他字段
  //   categoryForm.id = '';
  //   categoryForm.name = '';
  //   categoryForm.orderNum = 0;
  //   categoryForm.nodeType = '6';
  //   categoryForm.status = '0';
  //   categoryForm.remark = '';
  //
  //   // 重置表单验证状态
  //   proxy.resetForm("categoryFormRef");
  //
  //   // 恢复父级信息
  //   categoryForm.parentId = currentParentId;
  //   categoryForm.parentName = currentParentName;
  // }

  // 提交目录表单
  // function submitCategoryForm() {
  //   proxy.$refs["categoryFormRef"].validate(valid => {
  //     if (valid) {
  //       // 强制设置为新增模式（即使误操作保留id也覆盖）
  //       const isEdit = !!categoryForm.id;
  //       const api = isEdit ? updateKnowledgeTree : addKnowledgeTree;

  //       // 构造提交数据（明确区分新增/更新）
  //       const payload = {
  //         id: isEdit ? categoryForm.id : '', // 新增时移除id
  //         parentId: categoryForm.parentId,         // 必须来自父节点点击
  //         name: categoryForm.name,
  //         orderNum: categoryForm.orderNum,
  //         nodeType: categoryForm.nodeType,
  //       };

  //       api(payload).then(res => {
  //         if (res.code === 200) {
  //           ElMessage.success(isEdit ? '修改成功' : '新增成功');
  //           loadTreeData(manageTreeData);  // 刷新弹框内左侧树
  //           loadTreeData(categoryTreeData); // 刷新主页面左侧树
  //           categoryManageOpen.value = false; // 关闭弹框
  //           resetCategory(); // 重置表单
  //         } else {
  //           ElMessage.error(res.msg || '操作失败');
  //         }
  //       });
  //     }
  //   });
  // }

  // 通用加载目录树方法
  // function loadTreeData(target, isManageTree = false) {
  //   selectKnowledgeTreeList({ nodeType: 6 }).then(res => {
  //     let tree = handleTree(res.data, 'id', 'parentId', 'children');
  //
  //     // 如果是管理树，只显示"我的试卷"及其子目录
  //     if (isManageTree && tree && tree.length > 0) {
  //       const myPaperFolder = tree.find(item => item.name === '我的试卷');
  //       if (myPaperFolder) {
  //         tree = [myPaperFolder]; // 只保留"我的试卷"及其子目录
  //       } else {
  //         tree = []; // 如果没找到"我的试卷"，则不显示任何目录
  //         ElMessage.warning('未找到"我的试卷"目录');
  //       }
  //     }
  //
  //     target.value = tree || [];
  //
  //     // 如果是初始加载，默认选择第一个目录
  //     if (target === categoryTreeData && tree && tree.length > 0 && !queryParams.folder) {
  //       // 默认选择第一个顶级目录，顶层目录folder传空
  //       queryParams.folder = '';
  //
  //       // 延迟执行，确保树渲染完成后再触发查询
  //       nextTick(() => {
  //         handleQuery();
  //       });
  //     }
  //   });
  // }

  // 打开目录管理弹窗
  // function openCategoryManageDialog() {
  //   categoryManageOpen.value = true;
  //   // 确保只加载"我的试卷"目录
  //   loadTreeData(manageTreeData, true); // 传入true表示这是管理树，只显示"我的试卷"目录
  // }

  // 弹窗关闭时重置状态
  // function closeCategoryManageDialog() {
  //   categoryManageOpen.value = false;
  //   manageCurrentNode.value = null;
  //   resetCategory();
  // }

  // 弹窗内：选中节点
  // function handleManageNodeClick(data) {
  //   manageCurrentNode.value = data;

  //   // 清空表单
  //   proxy.resetForm("categoryFormRef");

  //   // 设置父级信息
  //   categoryForm.parentId = data.id;      // 父级ID（隐藏字段）
  //   categoryForm.parentName = data.name;  // 父级名称（展示用）

  //   // 初始化其他字段
  //   categoryForm.id = '';          // 确保ID为空，表示新增
  //   categoryForm.name = '';        // 清空名称输入框
  //   categoryForm.orderNum = 0;     // 重置顺序
  //   categoryForm.nodeType = '6';   // 固定节点类型
  //   categoryForm.status = '0';     // 默认状态
  //   categoryForm.remark = '';      // 清空备注
  // }

  // 弹窗内：编辑目录
  // function handleEditCategory() {
  //   if (!manageCurrentNode.value) {
  //     ElMessage.warning('请先选择一个目录');
  //     return;
  //   }
  //
  //   // 检查是否是"我的试卷"目录或其子目录
  //   if (manageCurrentNode.value.parentId === "0" && manageCurrentNode.value.name !== '我的试卷') {
  //     ElMessage.warning('只有"我的试卷"目录及其子目录可以编辑');
  //     return;
  //   }
  //
  //   // 顶级"我的试卷"目录不允许编辑
  //   if (manageCurrentNode.value.parentId === "0" && manageCurrentNode.value.name === '我的试卷') {
  //     ElMessage.warning('顶级"我的试卷"目录不能编辑');
  //     return;
  //   }
  //
  //   categoryForm.id = manageCurrentNode.value.id;
  //   categoryForm.name = manageCurrentNode.value.name;
  //   categoryForm.parentId = manageCurrentNode.value.parentId;
  //   categoryForm.orderNum = manageCurrentNode.value.orderNum;
  //   categoryForm.nodeType = manageCurrentNode.value.nodeType || '6';
  //   categoryForm.status = manageCurrentNode.value.status || '0';
  //   categoryForm.remark = manageCurrentNode.value.remark || '';
  //   categoryTitle.value = '编辑目录';
  // }

  // 弹窗内：删除目录
  // function handleDeleteCategory() {
  //   if (!manageCurrentNode.value) {
  //     ElMessage.warning('请先选择一个目录');
  //     return;
  //   }
  //
  //   // 顶级目录不能删除
  //   if (manageCurrentNode.value.parentId === "0") {
  //     ElMessage.warning('顶级目录不能删除');
  //     return;
  //   }
  //
  //   // 确认是"我的试卷"的子目录
  //   let isMyPaperChild = false;
  //   const parentNode = manageTreeData.value.find(node => node.id === manageCurrentNode.value.parentId);
  //   if (parentNode && parentNode.name === '我的试卷') {
  //     isMyPaperChild = true;
  //   }
  //
  //   if (!isMyPaperChild) {
  //     ElMessage.warning('只能删除"我的试卷"下的目录');
  //     return;
  //   }
  //
  //   ElMessageBox.confirm(`确定要删除目录"${manageCurrentNode.value.name}"吗？`, '提示', {
  //     confirmButtonText: '确定',
  //     cancelButtonText: '取消',
  //     type: 'warning',
  //   }).then(() => {
  //     return delKnowledgeTree(manageCurrentNode.value.id);
  //   }).then(() => {
  //     ElMessage.success('删除成功');
  //     loadTreeData(manageTreeData, true); // 重新加载管理树，确保只显示"我的试卷"
  //     loadTreeData(categoryTreeData); // 重新加载主页面树
  //     manageCurrentNode.value = null;
  //     resetCategory();
  //   });
  // }

  onMounted(() => {
    getList();
    // loadTreeData(categoryTreeData);
    // 初始计算表格高度
    nextTick(() => {
      calcTableHeight();
      // 添加窗口大小变化监听
      window.addEventListener('resize', handleResize);
    });
  });

  // 组件销毁前移除事件监听
  onBeforeUnmount(() => {
    window.removeEventListener('resize', handleResize);
  });
</script>

<style scoped>
  .app-container {
    padding: 20px;
  }

  .header-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fff;
    padding: 10px 15px;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
    margin-bottom: 15px;
  }

  .title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin: 0;
  }

  .actions {
    display: flex;
    gap: 10px;
  }

  /* .left-container {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
    height: calc(100vh - 170px);
    display: flex;
    flex-direction: column;
  }

  .category-search {
    padding: 10px;
    border-bottom: 1px solid #eee;
  }

  .category-tree {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
  }

  .tree-node-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }

  .node-label {
    font-size: 14px;
    flex: 1;
  } */

  .right-container {
    background-color: #fff;
    padding: 20px;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
    display: flex;
    flex-direction: column;
  }

  .table-container {
    margin-bottom: 15px;
  }

  /* 表格滚动条样式 */
  .el-table {
    overflow: hidden;
    border-radius: 4px;
  }

  .el-table:deep(.el-scrollbar__wrap) {
    overflow-x: hidden;
  }

  .el-table:deep(.el-scrollbar__bar.is-vertical) {
    width: 8px;
  }

  .el-table:deep(.el-scrollbar__bar.is-vertical > div) {
    width: 100%;
    background-color: rgba(144, 147, 153, 0.3);
    border-radius: 4px;
  }

  /* 固定右侧操作列的样式调整 */
  .el-table:deep(.el-table__fixed-right) {
    box-shadow: -2px 0 6px rgba(0, 0, 0, 0.1);
    border-radius: 0 4px 4px 0;
  }

  .drawer-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .drawer-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
  }

  .drawer-content {
    padding: 10px;
    height: 100%;
  }

  /* 新增的基于试题列表页面的样式 */
  /* .paper-category-tree {
    font-size: 14px;
    font-weight: 500;
    padding-right: 1px;
  }

  .paper-category-tree:deep(.el-tree-node__content) {
    height: 36px;
    padding: 6px 0;
    margin: 2px 0;
    border-radius: 4px;
    transition: all 0.2s ease;
  }

  .paper-category-tree:deep(.el-tree-node.is-current > .el-tree-node__content) {
    background-color: #ecf5ff;
    font-weight: bold;
    color: #409eff;
    border-left: 3px solid #409eff;
  }

  .paper-category-tree:deep(.el-tree-node__content:hover) {
    background-color: #f5f7fa;
    transform: translateX(3px);
  }

  .paper-category-tree:deep(.el-tree-node__children) {
    padding-left: 16px;
  }

  .category-tree::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    background-color: transparent;
    transition: all 0.3s 0.2s;
  }

  .category-tree::-webkit-scrollbar-thumb {
    background-color: rgba(192, 196, 204, 0);
    border-radius: 4px;
    transition: all 0.3s ease;
  }

  .category-tree:hover::-webkit-scrollbar {
    background-color: #f5f7fa;
  }

  .category-tree:hover::-webkit-scrollbar-thumb {
    background-color: #c0c4cc;
  }

  .edit-link {
    color: #409eff;
    cursor: pointer;
    margin-left: 8px;
    font-size: 14px;
    transition: color 0.2s;
  }

  .edit-link:hover {
    color: #66b1ff;
    text-decoration: underline;
  } */
</style>

