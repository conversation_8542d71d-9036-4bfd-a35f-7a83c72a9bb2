<template>
  <div class="app-container" v-loading="pageLoading" 
       element-loading-text="加载中..."
       element-loading-spinner="el-icon-loading"
       element-loading-background="rgba(255, 255, 255, 0.8)">
    <!-- 只有在不加载时才显示内容 -->
    <el-card v-if="!pageLoading" class="paper-card">
      <template #header>
        <div class="card-header">
          <div class="paper-title-container">
            <div class="paper-title-wrapper">
              <span v-if="!isEditingTitle" class="paper-title">{{ paperForm.paperName }}</span>
              <el-input
                  v-else
                  ref="titleInputRef"
                  v-model="paperForm.paperName"
                  class="paper-title-input"
                  placeholder="请输入试卷名称"
                  @blur="confirmTitleEdit"
              />
              <el-button
                  v-if="!isEditingTitle"
                  class="edit-title-btn"
                  icon="Edit"
                  size="small"
                  text
                  type="primary"
                  @click="startTitleEdit"
              />
              <el-button
                  v-else
                  class="confirm-title-btn"
                  icon="Check"
                  size="small"
                  text
                  type="success"
                  @click="confirmTitleEdit"
              />
            </div>
          </div>
        </div>
      </template>

      <!-- 试题区域 -->
      <div class="questions-section">
        <template v-if="questionTypes && questionTypes.length > 0">
          <div v-for="(type, index) in questionTypes" :key="index">
            <h4 class="section-title">{{ type.label }}</h4>

            <el-empty v-if="!questions[type.value] || questions[type.value].length === 0" description="暂无试题"/>

            <div v-else class="question-list">
              <div
                  v-for="(row, index) in questions[type.value]"
                  :key="row.id"
                  class="question-item"
              >
                <div class="cell">
                  <!-- 元信息 -->
                  <div class="meta-info">
                    <!-- 知识点和难度 -->
                    <div class="info-row">
                      <dict-tag
                              :options="sys_qh_questions_type"
                              :value="row.questionType"
                              class="type-tag unified-tag"
                      />
                      <div class="info-item">
                        <span class="item-label">难度:</span>
                        <span class="difficulty-value">{{ getDifficultyLabel(row.difficulty) }}</span>
                      </div>

                      <div v-if="row.knowledgeTreeList?.length > 0" class="info-item knowledge">
                        <span class="item-label">知识点:</span>
                        <div class="knowledge-list">
                          <span
                              v-for="(item, idx) in row.knowledgeTreeList"
                              :key="idx"
                              class="knowledge-item"
                          >
                            {{ item?.name }}
                            <span
                                v-if="idx < row.knowledgeTreeList.length - 1"
                                class="separator"
                            >/</span>
                          </span>
                        </div>
                      </div>

                      <div class="info-item score">
                        <span class="item-label">分值:</span>
                        <el-input-number
                            v-model="row.score"
                            :max="100"
                            :min="1"
                            class="score-input"
                            controls-position="right"
                            size="small"
                            @change="calculateTotalScore"
                        />
                      </div>
                    </div>
                  </div>

                  <!-- 题干图片 -->
                  <div class="context-image">
                    <div class="index-image-wrapper">
                      <span class="question-index">{{ index + 1 }}.</span>
                      <el-image
                          :preview-src-list="[]"
                          :src="row.context"
                          class="question-image"
                          fit="contain"
                      >
                        <template #error>
                          <div class="image-error">图片加载失败</div>
                        </template>
                      </el-image>
                    </div>
                  </div>

                  <div class="answer-control">
                    <!-- 试题来源试卷信息 -->
                    <span v-if="row.sourcePaper || row.year || row.region" class="question-source">
                      <span v-if="row.sourcePaper">来源：{{ row.sourcePaper }}</span>
                      <span v-if="row.year" class="source-info-item">年份：{{ row.year }}</span>
                      <span v-if="row.region" class="source-info-item">地区：{{ formatRegion(row.region) }}</span>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>

        <template v-else>
          <el-empty description="无法获取题型数据，请检查系统配置"></el-empty>
        </template>
      </div>

      <!-- 操作按钮区 -->
      <div class="action-buttons">
        <div class="total-info">
          <el-tag effect="dark" type="info">总题数: {{ totalQuestionCount }}</el-tag>
          <el-tag effect="dark" type="success">总分: {{ totalScore }}</el-tag>
        </div>
        <div class="buttons-group">
          <el-button :disabled="!hasQuestions" type="primary" @click="savePaper">创建试卷</el-button>
        </div>
      </div>
    </el-card>
    <!-- 空占位元素，确保loading状态有足够的高度 -->
    <div v-else class="loading-placeholder"></div>

    <!-- 创建试卷抽屉 -->
    <el-drawer
      v-model="drawerVisible"
      title="创建试卷"
      direction="rtl"
      size="30%"
    >
      <el-form :model="drawerForm" :rules="drawerRules" label-width="100px">
        <el-form-item label="试卷名称" prop="paperName">
          <el-input v-model="drawerForm.paperName" placeholder="请输入试卷名称" />
        </el-form-item>
        <el-form-item label="试卷类型" prop="paperType">
          <el-select v-model="drawerForm.paperType" placeholder="请选择试卷类型" style="width: 100%">
            <el-option
              v-for="dict in sys_qh_paper_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="地区">
          <el-cascader
            v-model="drawerForm.region"
            :options="regionData"
            :props="{ value: 'value', label: 'label', children: 'children' }"
            clearable
            placeholder="请选择地区"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="年份">
          <el-date-picker
            v-model="drawerForm.year"
            type="year"
            placeholder="请选择年份"
            value-format="YYYY"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="组卷目录" prop="folderId">
          <div class="folder-selection" ref="folderSelectionRef">
            <el-input
              v-model="drawerForm.folderName"
              placeholder="请选择组卷目录"
              readonly
              @click="showFolderTree = true"
            >
              <template #append>
                <el-button icon="ArrowDown" @click.stop="showFolderTree = true"></el-button>
              </template>
            </el-input>
            <div v-show="showFolderTree" class="folder-tree-dropdown">
              <div class="category-search">
                <el-input
                  v-model="filterText"
                  clearable
                  placeholder="搜索目录名称"
                  prefix-icon="Search"
                />
              </div>
              <div class="folder-tree-container">
                <el-tree
                  ref="folderTreeRef"
                  :data="categoryTree"
                  :props="{ label: 'name', children: 'children' }"
                  node-key="id"
                  highlight-current
                  :expand-on-click-node="false"
                  @node-click="handleFolderNodeClick"
                  :filter-node-method="filterNode"
                >
                  <template #default="{ node, data }">
                    <span class="tree-node-content">
                      <span class="node-label">{{ data.name }}</span>
                    </span>
                  </template>
                </el-tree>
              </div>
              <div class="folder-tree-actions">
                <el-button size="small" type="primary" @click="confirmFolderSelection">确定</el-button>
                <el-button size="small" @click="showFolderTree = false">取消</el-button>
              </div>
            </div>
          </div>
        </el-form-item> -->
      </el-form>
      <template #footer>
        <div style="flex: auto;">
          <el-button @click="drawerVisible = false">取消</el-button>
          <el-button type="primary" :loading="submitLoading" @click="submitPaper">确认</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script name="TestHand" setup>
import {computed, getCurrentInstance, nextTick, onMounted, onActivated, onBeforeUnmount, reactive, ref, onUnmounted} from 'vue';
import {useRouter} from 'vue-router';
import {listQuestionBankBoard} from "@/api/qh/questionBank";
import {addPaper} from "@/api/qh/paper";
import {ElMessage} from 'element-plus';
import {codeToText} from "element-china-area-data";
// import {regionData} from "element-china-area-data";
// import {selectKnowledgeTreeList} from "@/api/qh/knowledgeTree";
// import {handleTree} from '@/utils/domino';

const router = useRouter();
const {proxy} = getCurrentInstance();

// 获取字典数据
let sys_qh_difficulty = ref([]);
let sys_qh_questions_type = ref([]);
let sys_qh_paper_type = ref([]);

// 试题类型
const questionTypes = ref([]);
// 初始化试题列表
const questions = reactive({});
// 表单数据
const paperForm = reactive({
  paperName: '',
});
// 抽屉相关
const drawerVisible = ref(false);
const drawerForm = reactive({
  paperName: '',
  paperType: '',
  // region: [], // 修改为数组，用于存储级联选择器的值
  // year: '',
  // folderId: '', // 改为folderId，存储目录ID
  // folderName: '' // 新增：用于存储当前选中节点的名称
});
const drawerRules = {
  paperName: [
    {required: true, message: '请输入试卷名称', trigger: 'blur'},
    {min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur'}
  ],
  paperType: [
    { required: true, message: '请选择试卷类型', trigger: 'change' }
  ],
  // folderId: [
  //   { required: true, message: '请选择组卷目录', trigger: 'change' }
  // ]
};
// 页面级别加载状态，默认为true，避免页面闪现
const pageLoading = ref(true);
// 提交按钮加载状态
const submitLoading = ref(false);
// 试卷标题编辑相关
const isEditingTitle = ref(false);
const titleInputRef = ref(null);
// 总分计算
const totalScore = ref(0);
// 试卷目录选项
// const categoryOptions = ref([]);
// 组卷目录树数据
// const categoryTree = ref([]);
// const folderTreeRef = ref(null);
// 控制目录树显示
// const showFolderTree = ref(false);
// 目录选择器DOM引用
// const folderSelectionRef = ref(null);
// 树节点过滤文本
// const filterText = ref('');

// 监听过滤文本变化
// watch(filterText, (val) => {
//   folderTreeRef.value?.filter(val);
// });

// 树节点过滤方法
// const filterNode = (value, data) => {
//   if (!value) return true;
//   return data.name.indexOf(value) !== -1;
// };

// 地区格式化方法，兼容数组和字符串格式
const formatRegion = (regionData) => {
  // 处理空值情况
  if (!regionData) {
    return '';
  }

  // 统一将输入转换为数组（确保至少3个元素）
  let regionCodes = [];
  if (Array.isArray(regionData)) {
    // 数组格式：截取前3个元素（确保是省、市、区三级）
    regionCodes = regionData.slice(0, 3);
  } else if (typeof regionData === 'string') {
    // 字符串格式：用逗号分割后取前3个元素
    regionCodes = regionData.split(',').map(code => code.trim()).filter(code => code).slice(0, 3);
  }

  // 确保有3个编码（不足则补空，避免数组长度不够）
  while (regionCodes.length < 3) {
    regionCodes.push('');
  }

  // 解析三级编码（省、市、区）
  const province = codeToText[regionCodes[0]] || ''; // 第一级：省
  const city = codeToText[regionCodes[1]] || '';     // 第二级：市
  const district = codeToText[regionCodes[2]] || ''; // 第三级：区

  // 处理特殊情况：如果市编码无效，但省编码有效（如直辖市）
  const validCity = city || province;
  // 拼接结果（过滤空值）
  const regionNames = [province, validCity, district].filter(name => name);

  // 特殊处理：如果只有一个有效名称（如直辖市）
  if (regionNames.length === 1) {
    return regionNames[0];
  }

  // 正常拼接为"省/市/区"
  return regionNames.join(' / ');
};

// 获取当前日期作为默认试卷名称
const getDefaultPaperName = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  return `${year}-${month}-${day} ${hours}-${minutes}-${seconds}-手动组卷`;
};

// 获取难度标签名称
const getDifficultyLabel = (difficultyValue) => {
  const difficulty = sys_qh_difficulty.value.find(item => item.value === difficultyValue);
  return difficulty ? difficulty.label : '未知';
};
// 计算总分
const calculateTotalScore = () => {
  let sum = 0;
  // 遍历所有题型的试题
  Object.keys(questions).forEach(type => {
    if (questions[type]) {
      questions[type].forEach(question => {
        sum += Number(question.score || 0);
      });
    }
  });
  totalScore.value = sum;
};

// 判断是否有试题
const hasQuestions = computed(() => {
  let count = 0;
  Object.keys(questions).forEach(type => {
    if (questions[type]) {
      count += questions[type].length;
    }
  });
  return count > 0;
});

// 总题数
const totalQuestionCount = computed(() => {
  let count = 0;
  Object.keys(questions).forEach(type => {
    if (questions[type]) {
      count += questions[type].length;
    }
  });
  return count;
});

// 题型优先级排序
const sortQuestionTypes = () => {
  // 定义题型的优先顺序
  const priorityOrder = ['单选题', '多选题', '填空题', '判断题', '解答题'];

  // 创建一个映射，用于存储每种题型的优先级
  const priorityMap = {};
  priorityOrder.forEach((type, index) => {
    priorityMap[type] = index;
  });

  // 对题型数组进行排序
  questionTypes.value.sort((a, b) => {
    // 获取a和b的优先级，如果不在预定义的优先顺序中，则赋予一个很大的值
    const priorityA = priorityMap[a.label] !== undefined ? priorityMap[a.label] : 999;
    const priorityB = priorityMap[b.label] !== undefined ? priorityMap[b.label] : 999;

    // 按优先级排序
    return priorityA - priorityB;
  });
};

// 清空现有题目数据
const clearQuestionsData = () => {
  // 清空各题型的试题数组
  Object.keys(questions).forEach(type => {
    if (questions[type]) {
      questions[type] = [];
    }
  });
  // 重置总分
  totalScore.value = 0;
};

// 初始化试题类型
const initQuestionTypes = () => {
  // 获取当前题型（包括备选题型）
  const currentTypes = questionTypes.value;

  // 初始化每种题型的试题数组
  if (currentTypes && Array.isArray(currentTypes)) {
    currentTypes.forEach(type => {
      if (!questions[type.value]) {
        questions[type.value] = [];
      }
    });
  }
};

// 开始编辑标题
const startTitleEdit = () => {
  isEditingTitle.value = true;
  nextTick(() => {
    titleInputRef.value?.focus();
  });
};

// 确认标题编辑
const confirmTitleEdit = () => {
  if (!paperForm.paperName || paperForm.paperName.trim() === '') {
    paperForm.paperName = getDefaultPaperName();
    ElMessage.warning('试卷名称不能为空，已恢复默认名称');
  }
  isEditingTitle.value = false;
};

// 获取试题栏数据
const getQuestionBoardData = async () => {
  try {
    // 确保页面处于加载状态
    pageLoading.value = true;
    // 清空现有题目数据，避免重复加载
    clearQuestionsData();

    const res = await listQuestionBankBoard();
    if (res.code === 200 && res.data) {
      // 按题型对试题进行分类
      const boardQuestions = res.data || [];
      // 记录有哪些实际题型被返回
      const actualQuestionTypes = new Set();
      boardQuestions.forEach(question => {
        // 确保题型存在
        const questionType = question.questionType;
        if (questions[questionType]) {
          // 记录实际题型
          actualQuestionTypes.add(questionType);
          // 使用接口返回的分值（转换为数字）
          question.score = Number(question.score || 5);
          questions[questionType].push(question);
        }
      });
      // 过滤题型，只保留后端实际返回的题型
      questionTypes.value = questionTypes.value.filter(type =>
        actualQuestionTypes.has(type.value)
      );
      // 重新排序题型
      sortQuestionTypes();
      // 计算总分
      calculateTotalScore();
    }
  } catch (error) {
    console.error('获取试题栏数据失败:', error);
    ElMessage.error('获取试题栏数据失败');
  } finally {
    // 延迟关闭加载状态，确保所有数据都准备好了
    setTimeout(() => {
      pageLoading.value = false;
    }, 500);
  }
};

// 刷新试题数据
const refreshQuestionData = () => {
  // 先设置为加载状态，然后再获取数据
  pageLoading.value = true;
  // 使用setTimeout确保DOM有时间更新显示加载状态
  setTimeout(() => {
    getQuestionBoardData();
  }, 100);
};

// 加载目录树数据
// const loadCategoryOptions = () => {
//   selectKnowledgeTreeList({ nodeType: 6 }).then(res => {
//     if (res.data) {
//       // 处理为树状结构
//       const tree = handleTree(res.data, "id", "parentId", "children");
//       categoryTree.value = tree || [];
//
//       // 如果有目录数据，默认选中第一个
//       if (categoryTree.value.length > 0) {
//         // 默认选中第一个节点
//         nextTick(() => {
//           if (folderTreeRef.value) {
//             // 设置默认选中的节点
//             folderTreeRef.value.setCurrentKey(categoryTree.value[0].id);
//             // 更新表单值
//             drawerForm.folderId = categoryTree.value[0].id;
//             // 保存当前选中节点名称，用于显示
//             drawerForm.folderName = categoryTree.value[0].name;
//           }
//         });
//       }
//     }
//   }).catch(error => {
//     console.error('获取目录数据失败:', error);
//     ElMessage.error('获取目录数据失败');
//   });
// };

// 处理组卷目录节点点击
// const handleFolderNodeClick = (node) => {
//   drawerForm.folderId = node.id;
//   drawerForm.folderName = node.name;
// };

// 确认组卷目录选择
// const confirmFolderSelection = () => {
//   if (drawerForm.folderId) {
//     showFolderTree.value = false;
//   } else {
//     ElMessage.warning('请选择一个组卷目录');
//   }
// };

// 保存试卷 - 修改这个函数来显示抽屉
const savePaper = () => {
  if (!paperForm.paperName || paperForm.paperName.trim() === '') {
    ElMessage.warning('请输入试卷名称');
    return;
  }

  if (!hasQuestions.value) {
    ElMessage.warning('请至少添加一道题目');
    return;
  }

  // 打开抽屉，将当前试卷名称传到抽屉表单
  drawerForm.paperName = paperForm.paperName;

  // 确保目录数据已加载
  // if (categoryTree.value.length === 0) {
  //   loadCategoryOptions();
  // }

  drawerVisible.value = true;
};

// 提交试卷 - 新增此函数
const submitPaper = () => {
  if (!drawerForm.paperType) {
    ElMessage.warning('请选择试卷类型');
    return;
  }

  // if (!drawerForm.folderId) {
  //   ElMessage.warning('请选择组卷目录');
  //   return;
  // }

  // 设置加载状态为true
  submitLoading.value = true;

  try {
    // 构建保存试卷的数据结构
    const paperData = {
      cpaperName: drawerForm.paperName,
      cpaperType: drawerForm.paperType,
      // cregion: Array.isArray(drawerForm.region) ? drawerForm.region.join(',') : drawerForm.region, // 将数组转换为逗号分隔的字符串
      // cyear: drawerForm.year,
      // mulu: drawerForm.folderId, // 使用folderId作为目录ID
      paperStyle: '1',
      totalScore: totalScore.value,
      questionDTOList: []
    };

    // 将所有题型的试题整合
    Object.keys(questions).forEach(type => {
      if (questions[type]) {
        questions[type].forEach(question => {
          paperData.questionDTOList.push({
            id: question.id,
            questionType: question.questionType,
            score: question.score
          });
        });
      }
    });

    // 调用保存接口
    addPaper(paperData).then(res => {
      if (res.code === 200) {
        ElMessage.success('试卷创建成功');
        // 关闭抽屉
        drawerVisible.value = false;
        // 发出关闭事件，让父组件返回到试卷列表
        router.push('/cl/test');
      } else {
        ElMessage.error('创建试卷失败: ' + (res.msg || '未知错误'));
      }
    }).catch(error => {
      console.error('创建试卷失败:', error);
      ElMessage.error('创建试卷失败: ' + (error.message || '未知错误'));
    }).finally(() => {
      // 无论成功还是失败，都将加载状态设为false
      submitLoading.value = false;
    });
  } catch (error) {
    console.error('创建试卷失败:', error);
    ElMessage.error('创建试卷失败: ' + (error.message || '未知错误'));
    // 出现异常时也要关闭加载状态
    submitLoading.value = false;
  }
};

// 处理浏览器刷新的函数
const handleBrowserRefresh = () => {
  refreshQuestionData();
};

// 监听页面激活状态，当页面重新被访问时刷新数据
onActivated(() => {
  refreshQuestionData();
});

// 点击外部关闭树状选择框
// const handleClickOutside = (event) => {
//   const folderSelection = folderSelectionRef.value;
//   if (folderSelection && !folderSelection.contains(event.target) && showFolderTree.value) {
//     showFolderTree.value = false;
//   }
// };

onMounted(async () => {
  // 确保页面处于加载状态
  pageLoading.value = true;
  
  // 获取字典数据
  const dictData = proxy.useDict("sys_qh_difficulty", "sys_qh_questions_type", "sys_qh_paper_type");
  sys_qh_difficulty = dictData.sys_qh_difficulty;
  sys_qh_questions_type = dictData.sys_qh_questions_type;
  sys_qh_paper_type = dictData.sys_qh_paper_type;
  questionTypes.value = sys_qh_questions_type.value.map(item => ({
    label: item.label,
    value: item.value
  }));
  sortQuestionTypes();
  initQuestionTypes();
  // 设置默认试卷名称
  paperForm.paperName = getDefaultPaperName();
  // 获取试题栏数据
  await getQuestionBoardData();
  
  // 加载目录数据
  //loadCategoryOptions();

  // 添加浏览器刷新事件监听器
  window.addEventListener('load', handleBrowserRefresh);
  
  // 添加点击外部关闭树状选择框的事件监听
  // document.addEventListener('click', handleClickOutside);
});

// 组件销毁前移除事件监听器
onBeforeUnmount(() => {
  window.removeEventListener('load', handleBrowserRefresh);
  // document.removeEventListener('click', handleClickOutside);
});
</script>

<style scoped>
.app-container {
  padding: 20px;
  min-height: 500px; /* 确保有足够的高度显示加载动画 */
}

.loading-placeholder {
  min-height: 500px; /* 确保有足够的高度显示加载动画 */
}

.paper-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.paper-title-container {
  flex: 1;
  display: flex;
  justify-content: center;
}

.paper-title-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 300px;
  max-width: 500px;
}

.paper-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
  text-align: center;
  padding: 6px 15px;
}

.paper-title-input {
  max-width: 400px;
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  background-color: #ffffff;
  border-radius: 4px;
}

.paper-title-input :deep(.el-input__wrapper) {
  background-color: #ffffff;
  padding: 0 15px;
  box-shadow: 0 0 0 1px #e0e0e0 inset;
}

.paper-title-input :deep(.el-input__inner) {
  text-align: center;
  font-weight: bold;
  font-size: 18px;
  color: #303133;
}

.edit-title-btn,
.confirm-title-btn {
  position: absolute;
  right: -40px;
  top: 50%;
  transform: translateY(-50%);
}

.questions-section {
  margin-top: 20px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #1c1e22;
  position: relative;
  padding-left: 12px;
  margin: 15px 0;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background-color: #409EFF;
  border-radius: 2px;
}

.image-error {
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #f56c6c;
}

.action-buttons {
  margin-top: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.buttons-group {
  display: flex;
  gap: 10px;
}

.total-info {
  display: flex;
  gap: 10px;
}

.question-list {
  border: none;
}

.question-item {
  margin-bottom: 10px;
  border: 2px solid #ebeef5 !important;
  border-radius: 8px !important;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.question-item:hover {
  border-color: #d1e9ff !important;
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.meta-info {
  display: flex;
  flex-direction: column;
  background: #f8fbff;
  border-radius: 6px 6px 0 0;
  padding: 8px 15px 2px;
  border-bottom: 1px solid #ebeef5;
}

.tags-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
  padding-right: 1px;
}

.unified-tag {
  font-size: 18px;
  font-weight: 500;
  padding: 6px 5px;
  transform: scale(1.15);
  margin-right: 15px;
  margin-bottom: 5px;
}

.info-row {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin: -5px 0 5px;
  height: 24px;
}

.info-item {
  display: flex;
  align-items: center;
  font-size: 13px;
  margin-right: 20px;
}

.info-item.knowledge {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.info-item.score {
  margin-right: 0;
  margin-left: auto;
}

.item-label {
  color: #666;
  margin-right: 4px;
  display: inline-flex;
  align-items: center;
}

.difficulty-value {
  color: #f56c6c;
}

.knowledge-list {
  display: inline-flex;
  align-items: center;
  overflow: hidden;
  text-overflow: ellipsis;
}

.knowledge-item {
  color: #409eff;
  white-space: nowrap;
}

.separator {
  color: #c0c4cc;
  margin: 0 4px;
}

.score-input {
  width: 70px;
}

.score-input :deep(.el-input-number__decrease),
.score-input :deep(.el-input-number__increase) {
  height: 12px;
  line-height: 12px;
}

.context-image {
  border-radius: 4px;
  border: none;
  display: flex;
  justify-content: left;
  float: none;
  align-items: flex-start;
  position: relative;
  max-width: 800px;
  min-width: 600px;
  margin: 15px auto;
  padding: 0 30px;
}

.index-image-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 1px;
  width: 100%;
}

.question-index {
  position: absolute;
  top: 17px;
  left: 20px;
  color: #333;
  font-size: 16px;
  z-index: 10;
}

.answer-control {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background-color: #fafafa;
  border-top: 1px dashed #ebeef5;
}

.question-source {
  font-size: 14px;
  color: #606266;
  margin-right: auto;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.source-info-item {
  color: #67c23a;
  background-color: #f0f9eb;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.button-group {
  display: flex;
  gap: 10px;
  margin-left: auto;
  font-size: 12px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .action-buttons {
    flex-direction: column;
    gap: 15px;
  }

  .buttons-group {
    width: 100%;
    justify-content: space-between;
  }

  .total-info {
    justify-content: center;
    margin-bottom: 10px;
  }

  .info-row {
    flex-direction: column;
    align-items: flex-start;
    height: auto;
    gap: 5px;
  }

  .info-item {
    margin-right: 0;
  }

  .info-item.score {
    margin-left: 0;
  }
}

.type-tag {
  display: flex;
  align-self: center;
  margin-top: 8px;
}

/* 抽屉相关样式 */
.el-drawer__header {
  margin-bottom: 20px;
  padding: 16px 20px;
  border-bottom: 1px solid #ebeef5;
}

.el-drawer__body {
  padding: 0 20px;
}

.el-form-item {
  margin-bottom: 20px;
}

.el-drawer__footer {
  padding: 10px 20px;
  border-top: 1px solid #ebeef5;
  display: flex;
  justify-content: flex-end;
}

/* .folder-selection {
  display: flex;
  flex-direction: column;
  gap: 10px;
  position: relative;
}

.selected-folder {
  padding: 5px 0;
  display: flex;
  align-items: center;
  gap: 5px;
}

.folder-tree-container {
  max-height: 300px;
  overflow-y: auto;
  padding: 5px 0;
}

.folder-tree-container::-webkit-scrollbar {
  width: 6px;
}

.folder-tree-container::-webkit-scrollbar-thumb {
  background-color: #c0c4cc;
  border-radius: 3px;
}

.folder-tree-container::-webkit-scrollbar-track {
  background-color: #f5f7fa;
}

.folder-tree-dropdown {
  position: absolute;
  top: 40px;
  left: 0;
  z-index: 2000;
  background-color: #fff;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  width: 100%;
  display: flex;
  flex-direction: column;
}

.folder-tree-container {
  max-height: 300px;
  overflow-y: auto;
  padding: 5px 0;
}

.folder-tree-actions {
  padding: 10px;
  border-top: 1px solid #ebeef5;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.category-search {
  padding: 10px 15px;
  border-bottom: 1px solid #ebeef5;
}

.folder-tree-container :deep(.el-tree) {
  background-color: transparent;
  color: #606266;
}

.folder-tree-container :deep(.el-tree-node__content) {
  height: 32px;
  border-radius: 4px;
}

.folder-tree-container :deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #ecf5ff;
  color: #409EFF;
}

.folder-tree-container :deep(.el-tree-node__content:hover) {
  background-color: #f5f7fa;
} */

/* 树节点样式 */
.tree-node-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding-right: 8px;
}

.node-label {
  font-size: 14px;
}

.edit-link {
  font-size: 12px;
  color: #409EFF;
  cursor: pointer;
}

.edit-link:hover {
  text-decoration: underline;
}
</style>
