<template>
  <div class="app-container">
    <div class="page-container">
      <!-- 左侧面板 -->
      <div class="left-panel">
        <div class="left-panel-section">
          <div class="section-title small-title">
            <span class="title-number">1</span>
            <span class="title-text">选择题库</span>
          </div>
          <!-- 题库选择 -->
          <div class="subject-select-container">
            <el-select
                    v-model="paperForm.libraryIds"
                    placeholder="请选择题库"
                    multiple
                    class="full-width-select"
                    @change="handleLibrariesChange">
              <el-option v-for="item in libraryOptions" :key="item.value" :label="item.label" :value="item.value"/>
            </el-select>
          </div>

          <div class="knowledge-chapter-container">
            <!-- 知识点选择 -->
            <div class="knowledge-section">
              <div class="section-title small-title">
                <span class="title-number">3</span>
                <span class="title-text">选择知识点</span>
              </div>
              <div class="knowledge-tree-container">
                <el-input
                        v-model="knowledgeFilterText"
                        placeholder="搜索知识点"
                        clearable
                        size="small"
                        class="full-width-select"
                />
                <div class="selected-tags">
                  <div class="selected-count">已选择 {{ selectedKnowledgePoints.length }} 个</div>
                  <el-button type="text" @click="openSelectedKnowledgeDialog">查看</el-button>
                </div>
                <el-tree
                        ref="knowledgeTreeRef"
                        :data="knowledgeTreeData"
                        node-key="id"
                        :props="{
                    label: 'label',
                    children: 'children'
                  }"
                        :filter-node-method="filterNode"
                        show-checkbox
                        @check="handleKnowledgeCheck"
                />
              </div>
            </div>

            <!-- 章节选择 -->
            <div class="chapter-section">
              <div class="section-title small-title">
                <span class="title-number">2</span>
                <span class="title-text">选择章节</span>
              </div>
              <div class="chapter-tree-container">
                <el-input
                        v-model="chapterFilterText"
                        placeholder="搜索章节"
                        clearable
                        size="small"
                        class="full-width-select"
                />
                <div class="selected-tags">
                  <div class="selected-count">已选择 {{ selectedChapters.length }} 个</div>
                  <el-button type="text" @click="openSelectedChapterDialog">查看</el-button>
                </div>
                <el-tree
                        ref="chapterTreeRef"
                        :data="chapterTreeData"
                        node-key="id"
                        :props="{
                    label: 'label',
                    children: 'children'
                  }"
                        :filter-node-method="filterNode"
                        show-checkbox
                        @check="handleChapterCheck"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧面板 -->
      <div class="right-panel">
        <div class="right-panel-section">
          <div class="section-title small-title">
            <span class="title-number">4</span>
            <span class="title-text">组卷配置</span>
          </div>

          <!-- 年级选择 -->
          <div class="grade-section">
            <div class="grade-label">年级</div>
            <div class="grade-options">
              <div class="options-container">
                <div
                        class="option-item no-border"
                        :class="{ 'option-selected': paperForm.gradeIds.length === 0 }"
                        @click="clearGradeSelection"
                >
                  不限
                </div>
                <div
                        v-for="item in gradeOptions"
                        :key="item.value"
                        class="option-item no-border"
                        :class="{ 'option-selected': paperForm.gradeIds.includes(item.value) }"
                        @click="toggleGradeSelection(item.value)"
                >
                  {{ item.label }}
                </div>
              </div>
            </div>
          </div>

          <!-- 难度选择 -->
          <div class="difficulty-section">
            <div class="difficulty-label">难度</div>
            <div class="difficulty-picker">
              <el-select v-model="paperForm.difficulty" placeholder="请选择难度" class="medium-width-select">
                <el-option v-for="item in difficultyOptions" :key="item.value" :label="item.label" :value="item.value"/>
              </el-select>
            </div>
          </div>

          <!-- 地区选择 -->
          <div class="region-section">
            <div class="region-label">地区</div>
            <div class="region-tags-container">
              <el-tag
                      v-for="region in paperForm.regions"
                      :key="region"
                      closable
                      class="region-tag"
                      @close="removeRegion(region)"
              >
                {{ region }}
              </el-tag>
              <el-button size="small" @click="showRegionInput" style="margin-top: -2px;">
                添加地区
              </el-button>
            </div>

            <!-- 地区选择弹窗 -->
            <el-dialog v-model="regionInputVisible" title="添加地区" width="30%">
              <div class="region-input-container">
                <el-cascader
                  v-model="tempRegion"
                  :options="regionData"
                  :props="{ value: 'value', label: 'label', children: 'children' }"
                  clearable
                  placeholder="请选择地区"
                  style="width: 100%;"
                />
              </div>
              <template #footer>
                <span class="dialog-footer">
                  <el-button @click="regionInputVisible = false">取消</el-button>
                  <el-button type="primary" @click="addSelectedRegion">确定</el-button>
                </span>
              </template>
            </el-dialog>
          </div>

          <!-- 类型选择 -->
          <div class="type-section">
            <div class="type-label">类型</div>
            <div class="type-options">
              <div class="options-container">
                <div
                        class="option-item no-border"
                        :class="{ 'option-selected': paperForm.paperTypes.length === 0 }"
                        @click="clearTypeSelection"
                >
                  不限
                </div>
                <div
                        v-for="item in sys_qh_paper_type"
                        :key="item.value"
                        class="option-item no-border"
                        :class="{ 'option-selected': paperForm.paperTypes.includes(item.value) }"
                        @click="toggleTypeSelection(item.value)"
                >
                  {{ item.label }}
                </div>
              </div>
            </div>
          </div>

          <!-- 年份选择 -->
          <div class="year-section">
            <div class="year-label">年份</div>
            <div class="year-tags-container">
              <el-tag
                      v-for="year in paperForm.years"
                      :key="year"
                      closable
                      class="year-tag"
                      @close="removeYear(year)"
              >
                {{ year }}
              </el-tag>
              <el-button size="small" @click="showYearPicker" style=" margin-top: -2px;">
                添加年份
              </el-button>
            </div>

            <!-- 年份选择弹窗 -->
            <el-dialog v-model="yearPickerVisible" title="选择年份" width="30%">
              <div class="year-picker-container">
                <el-date-picker
                        v-model="tempYear"
                        type="year"
                        placeholder="选择年份"
                        format="YYYY"
                        value-format="YYYY"
                        style="width: 100%;"
                />
              </div>
              <template #footer>
                <span class="dialog-footer">
                  <el-button @click="yearPickerVisible = false">取消</el-button>
                  <el-button type="primary" @click="addSelectedYear">确定</el-button>
                </span>
              </template>
            </el-dialog>
          </div>

          <!-- 重置按钮 -->
          <div class="filter-actions">
            <div class="blank-space"></div>
            <el-button icon="Refresh" @click="resetForm">重置</el-button>
          </div>
        </div>

        <div class="right-panel-section">
          <div class="section-title">
            <span class="title-number">5</span>
            <span class="title-text">题型配置</span>
          </div>

          <div class="question-types-table">
            <el-table :data="questionTypeTableData" border style="width: 100%">
              <el-table-column prop="typeName" label="题型" width="180" />
              <el-table-column label="已选题数" width="400" align="center">
                <template #default="scope">
                  <div class="question-count-selector">
                    <div class="number-input-wrapper">
                      <span
                              class="number-control decrease"
                              :class="{ 'disabled': questionCounts[scope.row.type] <= 0 }"
                              @click="if(questionCounts[scope.row.type] > 0) { questionCounts[scope.row.type]--; handleQuestionCountChange(); }"
                      >-</span>
                      <el-input
                              v-model.number="questionCounts[scope.row.type]"
                              class="number-input"
                              size="small"
                              type="number"
                              :min="0"
                              :max="100"
                              @change="handleQuestionCountChange"
                      />
                      <span
                              class="number-control increase"
                              :class="{ 'disabled': questionCounts[scope.row.type] >= 100 }"
                              @click="if(questionCounts[scope.row.type] < 100) { questionCounts[scope.row.type]++; handleQuestionCountChange(); }"
                      >+</span>
                    </div>
                    <span class="count-unit">道</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="设置分值" width="400" align="center">
                <template #header>
                  <div class="points-header">
                    <el-checkbox v-model="paperForm.setPoints" class="points-toggle">设置分值</el-checkbox>
                  </div>
                </template>
                <template #default="scope">
                  <template v-if="paperForm.setPoints">
                    <div class="score-input-container">
                      <el-input
                              v-model="typeConfigs[scope.row.type].score"
                              class="score-input"
                              :disabled="questionCounts[scope.row.type] === 0"
                              size="small"
                              type="number"
                              :min="1"
                              :max="100"
                      />
                      <span class="score-unit">分/题</span>
                    </div>
                  </template>
                  <template v-else>
                    <span class="disabled-points">-</span>
                  </template>
                </template>
              </el-table-column>
              <el-table-column label="明细" align="center">
                <template #header>
                  <div class="config-header">
                    <el-checkbox v-model="paperForm.useDetailConfig" class="config-toggle">使用明细配置</el-checkbox>
                  </div>
                </template>
                <template #default="scope">
                  <el-button
                          type="primary"
                          size="small"
                          :disabled="questionCounts[scope.row.type] === 0 || !paperForm.useDetailConfig"
                          @click="showTypeConfigDialog(scope.row.type)"
                  >
                    配置
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <div class="submit-button">
            <div class="blank-space"></div>
            <el-button size="default" type="primary" @click="generatePaper">生成试卷</el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 保留现有对话框 -->
    <!-- 知识点选择对话框 -->
    <el-dialog v-model="knowledgeDialogVisible" title="选择知识点" width="50%">
      <el-tree
              ref="knowledgeTreeRef"
              :data="knowledgeTreeData"
              :default-checked-keys="currentQuestion?.knowledgePoints || []"
              node-key="id"
              show-checkbox
              :props="{
            label: 'label',
            children: 'children'
          }"
      />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="knowledgeDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmKnowledgePoints">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 已选知识点查看对话框 -->
    <el-dialog v-model="selectedKnowledgeDialogVisible" title="已选知识点" width="50%">
      <div v-if="selectedKnowledgePoints.length === 0" class="empty-selected">
        暂无选择的知识点
      </div>
      <el-table v-else :data="knowledgePointsTable" border style="width: 100%">
        <!--        <el-table-column prop="id" label="ID" width="180" />-->
        <el-table-column prop="label" label="知识点名称" width="750" />
        <el-table-column label="操作">
          <template #default="scope">
            <el-button icon="Delete" type="danger" link @click="removeSelectedKnowledgePoint(scope.row.id)">
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="selectedKnowledgeDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="clearSelectedKnowledgePoints">清空</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 已选章节查看对话框 -->
    <el-dialog v-model="selectedChapterDialogVisible" title="已选章节" width="50%">
      <div v-if="selectedChapters.length === 0" class="empty-selected">
        暂无选择的章节
      </div>
      <el-table v-else :data="chaptersTable" border style="width: 100%">
        <!--        <el-table-column prop="id" label="ID" width="180" />-->
        <el-table-column prop="label" label="章节名称" width="750"/>
        <el-table-column label="操作">
          <template #default="scope">
            <el-button icon="Delete" type="danger" link @click="removeSelectedChapter(scope.row.id)">
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="selectedChapterDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="clearSelectedChapters">清空</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 题型知识点选择对话框 -->
    <el-dialog v-model="typeKnowledgeDialogVisible" title="选择知识点" width="50%">
      <el-tree
              ref="typeKnowledgeTreeRef"
              :data="knowledgeTreeData"
              :default-checked-keys="currentType ? typeConfigs[currentType].knowledgePoints : []"
              node-key="id"
              show-checkbox
              :props="{
            label: 'label',
            children: 'children'
          }"
      />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="typeKnowledgeDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmTypeKnowledgePoints">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 章节选择对话框 -->
    <el-dialog v-model="chapterDialogVisible" title="选择章节" width="50%">
      <el-tree
              ref="chapterTreeRef"
              :data="chapterTreeData"
              :default-checked-keys="currentQuestion?.chapters || []"
              node-key="id"
              show-checkbox
              :props="{
            label: 'label',
            children: 'children'
          }"
      />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="chapterDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmChapterPoints">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 题型章节选择对话框 -->
    <el-dialog v-model="typeChapterDialogVisible" title="选择章节" width="50%">
      <el-tree
              ref="typeChapterTreeRef"
              :data="chapterTreeData"
              :default-checked-keys="currentType ? typeConfigs[currentType].chapters : []"
              node-key="id"
              show-checkbox
              :props="{
            label: 'label',
            children: 'children'
          }"
      />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="typeChapterDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmTypeChapterPoints">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 题型详细配置对话框 -->
    <el-dialog v-model="typeConfigDialogVisible" :title="`${currentTypeName}配置`" width="80%">
      <el-table :data="currentTypeItems" border style="width: 100%; margin-bottom: 20px;">
        <el-table-column label="题号" width="80" align="center">
          <template #default="scope">
            {{ `${scope.$index + 1}` }}
          </template>
        </el-table-column>
        <el-table-column label="难度" width="120">
          <template #default="scope">
            <el-select v-model="scope.row.difficulty" placeholder="选择难度" class="full-width-select">
              <el-option v-for="item in difficultyOptions" :key="item.value" :label="item.label" :value="item.value"/>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="年级" width="120">
          <template #default="scope">
            <el-select v-model="scope.row.gradeId" placeholder="选择年级" class="full-width-select">
              <el-option v-for="item in gradeOptions" :key="item.value" :label="item.label" :value="item.value"/>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="知识点" width="120">
          <template #default="scope">
            <el-button link type="primary" @click="showItemKnowledgeDialog(scope.row)">
              {{ scope.row.knowledgePoints.length > 0 ? `已选择${scope.row.knowledgePoints.length}个` : '选择知识点' }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="章节" width="120">
          <template #default="scope">
            <el-button link type="primary" @click="showItemChapterDialog(scope.row)">
              {{ scope.row.chapters.length > 0 ? `已选择${scope.row.chapters.length}个` : '选择章节' }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="地区" width="120">
          <template #default="scope">
            <el-input v-model="scope.row.region" placeholder="输入地区"/>
          </template>
        </el-table-column>
        <el-table-column label="年份" width="140">
          <template #default="scope">
            <el-input v-model="scope.row.year" placeholder="输入年份"/>
          </template>
        </el-table-column>
        <el-table-column label="试卷类型" width="120">
          <template #default="scope">
            <el-select v-model="scope.row.paperType" placeholder="选择类型" class="full-width-select">
              <el-option v-for="item in sys_qh_paper_type" :key="item.value" :label="item.label" :value="item.value"/>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="标签">
          <template #default="scope">
            <div class="tags-container">
              <el-tag
                      v-for="tag in scope.row.tags"
                      :key="tag"
                      class="question-tag"
                      closable
                      @close="handleItemRemoveTag(scope.row, tag)"
              >
                {{ tag }}
              </el-tag>
              <el-input
                      v-if="scope.row.tagInputVisible"
                      ref="itemTagInputRef"
                      v-model="scope.row.tagInputValue"
                      class="tag-input"
                      size="small"
                      @blur="handleItemTagConfirm(scope.row)"
                      @keyup.enter="handleItemTagConfirm(scope.row)"
              />
              <el-button v-else size="small" @click="showItemTagInput(scope.row)">
                + 添加标签
              </el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="分值" width="150">
          <template #default="scope">
            <el-input-number
                    v-model="scope.row.score"
                    :min="1"
                    :max="100"
                    size="small"
            />
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="typeConfigDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveTypeConfig">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 单个题目知识点选择对话框 -->
    <el-dialog v-model="itemKnowledgeDialogVisible" title="选择知识点" width="50%">
      <el-tree
              ref="itemKnowledgeTreeRef"
              :data="knowledgeTreeData"
              :default-checked-keys="currentItem?.knowledgePoints || []"
              node-key="id"
              show-checkbox
              :props="{
          label: 'label',
          children: 'children'
        }"
      />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="itemKnowledgeDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmItemKnowledgePoints">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 单个题目章节选择对话框 -->
    <el-dialog v-model="itemChapterDialogVisible" title="选择章节" width="50%">
      <el-tree
              ref="itemChapterTreeRef"
              :data="chapterTreeData"
              :default-checked-keys="currentItem?.chapters || []"
              node-key="id"
              show-checkbox
              :props="{
          label: 'label',
          children: 'children'
        }"
      />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="itemChapterDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmItemChapterPoints">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 创建试卷抽屉 -->
    <el-drawer
            v-model="drawerVisible"
            title="创建试卷"
            direction="rtl"
            size="30%"
    >
      <el-form :model="drawerForm" :rules="drawerRules" label-width="100px">
        <el-form-item label="试卷名称" prop="paperName">
          <el-input v-model="drawerForm.paperName" placeholder="请输入试卷名称" />
        </el-form-item>
        <el-form-item label="试卷类型" prop="paperType">
          <el-select v-model="drawerForm.paperType" placeholder="请选择试卷类型" style="width: 100%">
            <el-option
                    v-for="dict in sys_qh_paper_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="地区">
          <el-input v-model="drawerForm.region" placeholder="请输入地区" />
        </el-form-item>
        <el-form-item label="年份">
          <el-input v-model="drawerForm.year" placeholder="请输入年份" />
        </el-form-item>
        <el-form-item label="组卷目录" prop="folderId">
          <el-select v-model="drawerForm.folderId" placeholder="请选择组卷目录" style="width: 100%">
            <el-option
                    v-for="item in categoryTree"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div style="flex: auto;">
          <el-button @click="drawerVisible = false">取消</el-button>
          <el-button type="primary" @click="submitPaper">确认</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script name="PaperIndex" setup>
  import {computed, getCurrentInstance, nextTick, onMounted, reactive, ref, watch, onBeforeUnmount} from 'vue'
import {codeToText, regionData} from "element-china-area-data"
  import {ElLoading, ElMessage} from 'element-plus'
  import request from '@/utils/request'
  import {useRouter} from 'vue-router'

  const router = useRouter()
  const {proxy} = getCurrentInstance()
  const {sys_qh_questions_type, sys_qh_difficulty, sys_qh_paper_type, sys_qh_year_type} = proxy.useDict("sys_qh_questions_type", "sys_qh_difficulty", "sys_qh_paper_type", "sys_qh_year_type")

  // 题库、知识点、章节过滤文本
  const knowledgeFilterText = ref('')
  const chapterFilterText = ref('')

  // 已选中的知识点和章节
  const selectedKnowledgePoints = ref([])
  const selectedChapters = ref([])

  // 已选对话框可见性
  let selectedKnowledgeDialogVisible = ref(false)
  let selectedChapterDialogVisible = ref(false)

  // 已选知识点和章节的表格数据
  const knowledgePointsTable = computed(() => {
    return selectedKnowledgePoints.value.map(id => {
      // 通过ID查找对应的知识点详细信息
      const findNode = (nodes) => {
        for (const node of nodes) {
          if (node.id === id) return node
          if (node.children && node.children.length > 0) {
            const found = findNode(node.children)
            if (found) return found
          }
        }
        return null
      }
      const node = findNode(knowledgeTreeData.value) || { id, label: `知识点 ${id}` }
      return { id, label: node.label }
    })
  })

  const chaptersTable = computed(() => {
    return selectedChapters.value.map(id => {
      // 通过ID查找对应的章节详细信息
      const findNode = (nodes) => {
        for (const node of nodes) {
          if (node.id === id) return node
          if (node.children && node.children.length > 0) {
            const found = findNode(node.children)
            if (found) return found
          }
        }
        return null
      }
      const node = findNode(chapterTreeData.value) || { id, label: `章节 ${id}` }
      return { id, label: node.label }
    })
  })

  // 过滤节点的方法（用于知识点和章节树）
  const filterNode = (value, data) => {
    if (!value) return true
    return data.label.toLowerCase().includes(value.toLowerCase())
  }

  // 监听知识点过滤文本变化
  watch(knowledgeFilterText, (val) => {
    proxy.$refs.knowledgeTreeRef?.filter(val)
  })

  // 监听章节过滤文本变化
  watch(chapterFilterText, (val) => {
    proxy.$refs.chapterTreeRef?.filter(val)
  })

  // 处理题库变更
  const handleLibrariesChange = (value) => {
    // 当题库选择变化时，重新获取章节数据
    if (value.length > 0) {
      fetchChapterData()
    } else {
      // 如果没有选择题库，清空章节树数据
      chapterTreeData.value = []
    }
    // 清空已选章节和知识点
    selectedChapters.value = []
    selectedKnowledgePoints.value = []
    knowledgeTreeData.value = []
    
    // 如果有章节树引用，重置选中状态
    if (proxy.$refs.chapterTreeRef) {
      proxy.$refs.chapterTreeRef.setCheckedKeys([])
    }
    
    // 如果有知识点树引用，重置选中状态
    if (proxy.$refs.knowledgeTreeRef) {
      proxy.$refs.knowledgeTreeRef.setCheckedKeys([])
    }
  }

  // 处理知识点选择
  const handleKnowledgeCheck = (data, { checkedKeys }) => {
    selectedKnowledgePoints.value = checkedKeys
  }

  // 处理章节选择
  const handleChapterCheck = (data, { checkedKeys, checkedNodes, halfCheckedKeys, halfCheckedNodes }) => {
    // 获取之前的选中状态
    const prevSelectedChapters = [...selectedChapters.value]
    // 更新当前选中状态
    selectedChapters.value = checkedKeys
    
    // 检查是否有章节被取消选中
    const hasUnselected = prevSelectedChapters.some(id => !checkedKeys.includes(id))
    
    // 如果有章节被取消选中，重新获取知识点数据
    if (hasUnselected) {
      // 如果当前没有选中的章节，清空知识点树
      if (checkedKeys.length === 0) {
        knowledgeTreeData.value = []
        selectedKnowledgePoints.value = []
        if (proxy.$refs.knowledgeTreeRef) {
          proxy.$refs.knowledgeTreeRef.setCheckedKeys([])
        }
      } else {
        // 否则基于剩余选中的章节重新获取知识点
        fetchKnowledgeData()
        // 保留只属于当前选中章节的知识点
        // 注意：这里可能需要根据实际业务逻辑调整
        // 如果知识点与章节没有明确的从属关系，这部分逻辑可能需要修改
      }
    } else if (checkedKeys.length > prevSelectedChapters.length) {
      // 如果有新增章节选中，也重新获取知识点数据
      fetchKnowledgeData()
    }
  }

  // 打开已选知识点对话框
  const openSelectedKnowledgeDialog = () => {
    selectedKnowledgeDialogVisible.value = true
  }

  // 打开已选章节对话框
  const openSelectedChapterDialog = () => {
    selectedChapterDialogVisible.value = true
  }

  // 移除选中的知识点
  const removeSelectedKnowledgePoint = (id) => {
    selectedKnowledgePoints.value = selectedKnowledgePoints.value.filter(item => item !== id)
    proxy.$refs.knowledgeTreeRef?.setChecked(id, false)
  }

  // 移除选中的章节
  const removeSelectedChapter = (id) => {
    selectedChapters.value = selectedChapters.value.filter(item => item !== id)
    proxy.$refs.chapterTreeRef?.setChecked(id, false)
  }

  // 清空所有选中的知识点
  const clearSelectedKnowledgePoints = () => {
    selectedKnowledgePoints.value = []
    // 更新树的选中状态
    proxy.$refs.knowledgeTreeRef?.setCheckedKeys([])
    selectedKnowledgeDialogVisible.value = false
  }

  // 清空所有选中的章节
  const clearSelectedChapters = () => {
    selectedChapters.value = []
    // 更新树的选中状态
    proxy.$refs.chapterTreeRef?.setCheckedKeys([])
    selectedChapterDialogVisible.value = false
  }

  // 表单数据
  const paperFormRef = ref(null)
  const paperForm = reactive({
    gradeIds: [],
    paperTypes: [],
    paperName: '',
    libraryIds: [],
    difficulty: '',
    region: '', // 保留原始的单一地区字段，便于兼容
    regions: [], // 新增地区数组字段
    years: [],
    hasAnalysis: false,
    hasChoices: true,
    hasFillBlanks: true,
    hasSolution: true,
    hasExploration: true,
    setPoints: false,
    useDetailConfig: false // 是否使用明细配置
  })

  // 表单验证规则
  const rules = {
    paperName: [{required: true, message: '请输入试卷名称', trigger: 'blur'}],
    libraryIds: [{type: 'array', required: false, message: '请选择题库', trigger: 'change'}]
  }

  // 模拟数据
  const libraryOptions = ref([])
  const gradeOptions = ref([])

  // 使用字典数据的难度选项
  const difficultyOptions = computed(() => {
    return sys_qh_difficulty.value.map(item => ({
      value: item.value,
      label: item.label
    }));
  })

  // 题型定义 (可以保留作为备用)
  const questionTypeLabels = {
    singleChoice: '单选题',
    multiChoice: '多选题',
    judgment: '判断题',
    fillBlank: '填空题',
    shortAnswer: '解答题'
  }

  // 题型映射关系
  const questionTypeMapping = {
    singleChoice: '1',
    multiChoice: '2',
    judgment: '3',
    fillBlank: '4',
    shortAnswer: '5'
  }

  // 获取题型标签
  const getQuestionTypeLabel = (type) => {
    const typeCode = questionTypeMapping[type]
    const dictItem = sys_qh_questions_type.value.find(item => item.value === typeCode)
    return dictItem ? dictItem.label : questionTypeLabels[type]
  }

  // 题型数量
  const questionCounts = reactive({
    singleChoice: 0,
    multiChoice: 0,
    judgment: 0,
    fillBlank: 0,
    shortAnswer: 0
  })

  // 标记每种题型是否已进行明细配置
  const hasDetailConfigured = reactive({
    singleChoice: false,
    multiChoice: false,
    judgment: false,
    fillBlank: false,
    shortAnswer: false
  })

  // 简化模式下的题型配置
  // 设置默认难度为"正常"(值为2)
const defaultDifficulty = computed(() => '2')

  const typeConfigs = reactive({
    singleChoice: {
      difficulty: '',
      gradeId: '',
      paperType: '',
      knowledgePoints: [],
      chapters: [],
      region: '',
      year: '',
      tags: [],
      tagInputVisible: false,
      tagInputValue: '',
      score: 3
    },
    multiChoice: {
      difficulty: '',
      gradeId: '',
      paperType: '',
      knowledgePoints: [],
      chapters: [],
      region: '',
      year: '',
      tags: [],
      tagInputVisible: false,
      tagInputValue: '',
      score: 4
    },
    judgment: {
      difficulty: '',
      gradeId: '',
      paperType: '',
      knowledgePoints: [],
      chapters: [],
      region: '',
      year: '',
      tags: [],
      tagInputVisible: false,
      tagInputValue: '',
      score: 2
    },
    fillBlank: {
      difficulty: '',
      gradeId: '',
      paperType: '',
      knowledgePoints: [],
      chapters: [],
      region: '',
      year: '',
      tags: [],
      tagInputVisible: false,
      tagInputValue: '',
      score: 3
    },
    shortAnswer: {
      difficulty: '',
      gradeId: '',
      paperType: '',
      knowledgePoints: [],
      chapters: [],
      region: '',
      year: '',
      tags: [],
      tagInputVisible: false,
      tagInputValue: '',
      score: 5
    }
  })

  // 设置初始难度值
  const initDifficultyValues = () => {
    const defaultValue = defaultDifficulty.value
    // 设置表单默认难度为"正常"
    paperForm.difficulty = defaultValue
    // 设置各题型默认难度
    for (const type in typeConfigs) {
      typeConfigs[type].difficulty = defaultValue
    }
  }

  // 题目列表
  const questions = reactive({
    singleChoice: [],
    multiChoice: [],
    judgment: [],
    fillBlank: [],
    shortAnswer: []
  })

  // 计算属性：是否有题目
  const hasQuestions = computed(() => {
    return Object.values(questionCounts).some(count => count > 0)
  })

  // 计算属性：每种题型的总分
  const typeScores = computed(() => {
    const scores = {}
    for (const type in questions) {
      scores[type] = questions[type].reduce((sum, q) => sum + q.score, 0)
    }
    return scores
  })

  // 计算属性：总分
  const totalScore = computed(() => {
    let sum = 0
    for (const type in questions) {
      sum += questions[type].reduce((acc, q) => acc + q.score, 0)
    }
    return sum
  })

  // 当题目数量变化时
  const handleQuestionCountChange = () => {
    for (const type in questionCounts) {
      // 确保数量是有效数字且在范围内
      if (isNaN(questionCounts[type]) || questionCounts[type] < 0) {
        questionCounts[type] = 0;
      } else if (questionCounts[type] > 30) {
        questionCounts[type] = 30;
      } else {
        // 将输入转换为整数
        questionCounts[type] = Math.floor(questionCounts[type]);
      }

      const oldCount = questions[type].length;
      const newCount = questionCounts[type];

      if (newCount > oldCount) {
        // 增加题目
        for (let i = oldCount; i < newCount; i++) {
          questions[type].push({
            id: `${type}-${i + 1}`,
            difficulty: typeConfigs[type].difficulty, // 使用题型默认配置
            gradeId: typeConfigs[type].gradeId, // 使用题型默认年级
            paperType: typeConfigs[type].paperType, // 使用题型默认试卷类型
            knowledgePoints: [...typeConfigs[type].knowledgePoints], // 深拷贝知识点
            chapters: [...typeConfigs[type].chapters], // 深拷贝章节
            region: typeConfigs[type].region, // 使用题型默认地区
            year: typeConfigs[type].year, // 使用题型默认年份
            tags: [...typeConfigs[type].tags], // 深拷贝标签
            tagInputVisible: false,
            tagInputValue: '',
            score: typeConfigs[type].score // 使用题型默认分值
          });
        }
      } else if (newCount < oldCount) {
        // 减少题目
        questions[type].splice(newCount, oldCount - newCount);
      }
    }
    calculateTotalScore();
  }

  // 应用题型配置到所有该类型题目
  const applyTypeConfig = (type) => {
    const config = typeConfigs[type]

    questions[type].forEach(question => {
      question.difficulty = config.difficulty
      question.gradeId = config.gradeId
      question.paperType = config.paperType
      question.region = config.region
      question.year = config.year
      question.score = config.score
      // 知识点、章节和标签不进行批量修改，保持各题目的独立性
    })

    calculateTotalScore()
  }

  // 默认分值
  const getDefaultScore = (type) => {
    return typeConfigs[type].score || 2
  }

  // 获取特定类型的题目
  const getQuestionsByType = (type) => {
    return questions[type]
  }

  // 计算总分
  const calculateTotalScore = () => {
    // 计算已经在computed中完成
  }

  // 删除题目
  const removeQuestion = (type, index) => {
    questions[type].splice(index, 1)
    questionCounts[type] -= 1
    calculateTotalScore()
  }

  // 删除整个题型
  const removeQuestionType = (type) => {
    questionCounts[type] = 0
    questions[type] = []
    calculateTotalScore()
  }

  // 知识点选择相关
  let knowledgeDialogVisible = ref(false)
  const knowledgeTreeRef = ref(null)
  const currentQuestion = ref(null)

  // 题型知识点选择相关
  let typeKnowledgeDialogVisible = ref(false)
  const typeKnowledgeTreeRef = ref(null)
  const currentType = ref(null)

  // 模拟的知识树数据
  const knowledgeTreeData = ref([])

  // 显示知识点对话框
  const showKnowledgeDialog = (question) => {
    currentQuestion.value = question
    knowledgeDialogVisible.value = true
  }

  // 确认选择的知识点
  const confirmKnowledgePoints = () => {
    if (currentQuestion.value && knowledgeTreeRef.value) {
      currentQuestion.value.knowledgePoints = knowledgeTreeRef.value.getCheckedKeys()
    }
    knowledgeDialogVisible.value = false
  }

  // 显示题型知识点对话框
  const showTypeKnowledgeDialog = (type) => {
    currentType.value = type
    typeKnowledgeDialogVisible.value = true
  }

  // 确认选择的题型知识点
  const confirmTypeKnowledgePoints = () => {
    if (currentType.value && typeKnowledgeTreeRef.value) {
      const type = currentType.value
      typeConfigs[type].knowledgePoints = typeKnowledgeTreeRef.value.getCheckedKeys()

      // 如果希望自动应用到所有该类型的题目，可以取消注释下面的代码
      // questions[type].forEach(question => {
      //   question.knowledgePoints = [...typeConfigs[type].knowledgePoints]
      // })
    }
    typeKnowledgeDialogVisible.value = false
  }

  // 标签相关
  const tagInputRef = ref(null)
  const typeTagInputRef = ref(null)

  // 显示标签输入框
  const showTagInput = (question) => {
    question.tagInputVisible = true
    nextTick(() => {
      tagInputRef.value?.focus()
    })
  }

  // 确认添加标签
  const handleTagConfirm = (question) => {
    const inputValue = question.tagInputValue
    if (inputValue && !question.tags.includes(inputValue)) {
      question.tags.push(inputValue)
    }
    question.tagInputVisible = false
    question.tagInputValue = ''
  }

  // 移除标签
  const handleRemoveTag = (question, tag) => {
    question.tags.splice(question.tags.indexOf(tag), 1)
  }

  // 显示题型标签输入框
  const showTypeTagInput = (type) => {
    typeConfigs[type].tagInputVisible = true
    nextTick(() => {
      typeTagInputRef.value?.focus()
    })
  }

  // 确认添加题型标签
  const handleTypeTagConfirm = (type) => {
    const inputValue = typeConfigs[type].tagInputValue
    if (inputValue && !typeConfigs[type].tags.includes(inputValue)) {
      typeConfigs[type].tags.push(inputValue)
    }
    typeConfigs[type].tagInputVisible = false
    typeConfigs[type].tagInputValue = ''
  }

  // 移除题型标签
  const handleRemoveTypeTag = (type, tag) => {
    typeConfigs[type].tags.splice(typeConfigs[type].tags.indexOf(tag), 1)
  }

  // 章节选择相关
  let chapterDialogVisible = ref(false)
  const chapterTreeRef = ref(null)
  let typeChapterDialogVisible = ref(false)
  const typeChapterTreeRef = ref(null)

  // 模拟的章节树数据
  const chapterTreeData = ref([])

  // 显示章节对话框
  const showChapterDialog = (question) => {
    currentQuestion.value = question
    chapterDialogVisible.value = true
  }

  // 确认选择的章节
  const confirmChapterPoints = () => {
    if (currentQuestion.value && chapterTreeRef.value) {
      currentQuestion.value.chapters = chapterTreeRef.value.getCheckedKeys()
    }
    chapterDialogVisible.value = false
  }

  // 显示题型章节对话框
  const showTypeChapterDialog = (type) => {
    currentType.value = type
    typeChapterDialogVisible.value = true
  }

  // 确认选择的题型章节
  const confirmTypeChapterPoints = () => {
    if (currentType.value && typeChapterTreeRef.value) {
      const type = currentType.value
      typeConfigs[type].chapters = typeChapterTreeRef.value.getCheckedKeys()
    }
    typeChapterDialogVisible.value = false
  }

  // 题型配置对话框相关
  let typeConfigDialogVisible = ref(false)
  const currentTypeName = ref('')
  const currentTypeConfig = reactive({
    difficulty: '',
    gradeId: '',
    paperType: '',
    knowledgePoints: [],
    chapters: [],
    region: '',
    year: '',
    tags: [],
    score: 0
  })

  // 当前题型中的题目列表
  const currentTypeItems = ref([])

  // 当前正在操作的单个题目
  const currentItem = ref(null)

  // 单个题目知识点和章节对话框
  let itemKnowledgeDialogVisible = ref(false)
  let itemChapterDialogVisible = ref(false)
  const itemKnowledgeTreeRef = ref(null)
  const itemChapterTreeRef = ref(null)
  const itemTagInputRef = ref(null)

  // 抽屉相关
  const drawerVisible = ref(false)
  const drawerForm = reactive({
    paperName: '',
    paperType: '',
    region: '',
    year: '',
    folderId: '' // 添加组卷目录ID字段
  })
  const drawerRules = {
    paperName: [
      { required: true, message: '请输入试卷名称', trigger: 'blur' }
    ],
    paperType: [
      { required: true, message: '请选择试卷类型', trigger: 'change' }
    ],
    folderId: [
      { required: true, message: '请选择组卷目录', trigger: 'change' }
    ]
  }

  // 组卷目录树数据
  const categoryTree = ref([])
  const folderTreeRef = ref(null)
  // 控制目录树显示
  const showFolderTree = ref(false)
  // 目录选择器DOM引用
  const folderSelectionRef = ref(null)

  // 显示题型配置对话框
  const showTypeConfigDialog = (type) => {
    currentType.value = type
    // 获取题型名称
    const typeCode = questionTypeMapping[type]
    const typeItem = sys_qh_questions_type.value.find(item => item.value === typeCode)
    currentTypeName.value = typeItem ? typeItem.label : questionTypeLabels[type]

    // 设置当前题型的题目列表
    currentTypeItems.value = [...questions[type]]

    typeConfigDialogVisible.value = true
  }

  // 显示单个题目知识点对话框
  const showItemKnowledgeDialog = (item) => {
    currentItem.value = item
    itemKnowledgeDialogVisible.value = true
  }

  // 确认单个题目知识点选择
  const confirmItemKnowledgePoints = () => {
    if (currentItem.value && itemKnowledgeTreeRef.value) {
      currentItem.value.knowledgePoints = itemKnowledgeTreeRef.value.getCheckedKeys()
    }
    itemKnowledgeDialogVisible.value = false
  }

  // 显示单个题目章节对话框
  const showItemChapterDialog = (item) => {
    currentItem.value = item
    itemChapterDialogVisible.value = true
  }

  // 确认单个题目章节选择
  const confirmItemChapterPoints = () => {
    if (currentItem.value && itemChapterTreeRef.value) {
      currentItem.value.chapters = itemChapterTreeRef.value.getCheckedKeys()
    }
    itemChapterDialogVisible.value = false
  }

  // 显示单个题目标签输入框
  const showItemTagInput = (item) => {
    item.tagInputVisible = true
    nextTick(() => {
      itemTagInputRef.value?.focus()
    })
  }

  // 确认添加单个题目标签
  const handleItemTagConfirm = (item) => {
    const inputValue = item.tagInputValue
    if (inputValue && !item.tags.includes(inputValue)) {
      item.tags.push(inputValue)
    }
    item.tagInputVisible = false
    item.tagInputValue = ''
  }

  // 移除单个题目标签
  const handleItemRemoveTag = (item, tag) => {
    item.tags.splice(item.tags.indexOf(tag), 1)
  }

  // 保存题型配置
  const saveTypeConfig = () => {
    if (!currentType.value) return

    const type = currentType.value

    // 更新questions中该类型的题目列表
    questions[type] = [...currentTypeItems.value]

    // 标记该题型已进行明细配置
    hasDetailConfigured[type] = true

    typeConfigDialogVisible.value = false
    ElMessage.success(`${currentTypeName.value}配置已保存`)
  }

  // 重置表单
  const resetForm = () => {
    // 重置表单字段
    paperForm.gradeIds = []
    paperForm.paperTypes = []
    paperForm.difficulty = defaultDifficulty.value // 重置为"正常"难度
    paperForm.region = ''
    paperForm.regions = [] // 重置regions数组
    paperForm.years = []
    paperForm.setPoints = false
    paperForm.useDetailConfig = false // 重置明细配置选项

    // 重置题型数量
    for (const type in questionCounts) {
      questionCounts[type] = 0
    }

    // 重置题目列表
    for (const type in questions) {
      questions[type] = []
    }

    // 重置明细配置标记
    for (const type in hasDetailConfigured) {
      hasDetailConfigured[type] = false
    }

    // 重置题型配置并设置默认难度
    for (const type in typeConfigs) {
      typeConfigs[type] = {
        difficulty: defaultDifficulty.value,
        gradeId: '',
        paperType: '',
        knowledgePoints: [],
        chapters: [],
        region: '',
        year: '',
        tags: [],
        tagInputVisible: false,
        tagInputValue: '',
        score: getDefaultScore(type)
      }
    }

    // 提示用户
    ElMessage.success('已重置所有筛选条件')
  }

  // 处理测试数据（模拟接口返回）
  const processTestData = (testData) => {
    if (testData && testData.code === 200 && Array.isArray(testData.data)) {
      // 不再显示预览对话框
      ElMessage.success('自动组卷成功！')
      router.push('/cl/test');
      return true
    }
    return false
  }

  // 生成试卷，显示抽屉
  const generatePaper = () => {
    // 移除表单验证逻辑，直接展示抽屉
    if (!hasQuestions.value) {
      ElMessage.warning('请至少添加一道题目')
      return
    }

    // 设置抽屉表单的初始值
    drawerForm.paperName = paperForm.paperName || '新建试卷'
    drawerForm.paperType = paperForm.paperTypes[0]
    drawerForm.region = paperForm.region
    drawerForm.year = paperForm.years[0]
    drawerForm.folderId = ''

    // 加载组卷目录数据
    //loadCategoryOptions()

    // 显示抽屉
    drawerVisible.value = true
  }

  // 加载目录树数据
  // const loadCategoryOptions = () => {
  //   request({
  //     url: '/qh/knowledgeTree/list',
  //     method: 'get',
  //     params: { nodeType: 6 }
  //   }).then(res => {
  //     if (res.data) {
  //       // 处理为树状结构
  //       const tree = proxy.handleTree(res.data, "id", "parentId", "children");
  //       categoryTree.value = tree;
  //
  //       // 如果有目录数据，默认选中第一个
  //       if (categoryTree.value.length > 0) {
  //         // 默认选中第一个节点
  //         nextTick(() => {
  //           if (folderTreeRef.value) {
  //             // 设置默认选中的节点
  //             folderTreeRef.value.setCurrentKey(categoryTree.value[0].id);
  //             // 更新表单值
  //             drawerForm.folderId = categoryTree.value[0].id;
  //             // 保存当前选中节点名称，用于显示
  //             drawerForm.folderName = categoryTree.value[0].name;
  //           }
  //         });
  //       }
  //     }
  //   }).catch(error => {
  //     console.error('获取目录数据失败:', error);
  //     ElMessage.error('获取目录数据失败');
  //   });
  // };

  // 处理组卷目录节点点击
  const handleFolderNodeClick = (node) => {
    // 判断是否是顶层目录
    const isTopFolder = node.parentId === '0';
    
    // 如果是顶层目录，folder传空；非顶层目录，folder传值
    drawerForm.folderId = isTopFolder ? '' : node.id;
    drawerForm.folderName = node.name;
  };

  // 确认组卷目录选择
  const confirmFolderSelection = () => {
    if (drawerForm.folderId) {
      showFolderTree.value = false;
    } else {
      ElMessage.warning('请选择一个组卷目录');
    }
  };

  // 点击外部关闭树状选择框
  const handleClickOutside = (event) => {
    const folderSelection = folderSelectionRef.value;
    if (folderSelection && !folderSelection.contains(event.target) && showFolderTree.value) {
      showFolderTree.value = false;
    }
  };

  // 提交创建试卷
  const submitPaper = () => {
    // 检查是否勾选了使用明细配置但没有进行配置
    if (paperForm.useDetailConfig) {
      let hasAnyDetailConfig = false;
      // 检查是否有任何题型进行了明细配置
      for (const type in questionCounts) {
        if (questionCounts[type] > 0 && hasDetailConfigured[type]) {
          hasAnyDetailConfig = true;
          break;
        }
      }

      if (!hasAnyDetailConfig) {
        ElMessage.error('您勾选了使用明细配置，但未对任何题型进行明细配置，请点击配置按钮进行设置');
        return;
      }
    }

    // 构建前端内部区分的参数 (使用前缀区分)
    const dQuestionList = [] // d前缀 - 题目查询条件
    const eQuestionList = [] // e前缀 - 明细题目配置

    // 处理每种题型的基础配置 - 使用d前缀
    for (const type in questionCounts) {
      if (questionCounts[type] > 0) {
        // 使用字典映射获取题型代码
        const questionTypeCode = questionTypeMapping[type]

        // 题目查询条件 - 使用d前缀
        const duoDTO = {
          dquestionType: questionTypeCode,
          drequiredNumber: questionCounts[type],
          ddifficulty: typeConfigs[type].difficulty,
          dscore: typeConfigs[type].score.toString(),
          dknowledgePoints: selectedKnowledgePoints.value.map(String),
          dchapters: selectedChapters.value.map(String),
          dregion: paperForm.region,
          dregions: paperForm.regions, // 添加多地区参数
          dgradeIds: paperForm.gradeIds.length > 0 ? paperForm.gradeIds : [],
          dpaperTypes: paperForm.paperTypes.length > 0 ? paperForm.paperTypes : [],
          dyears: paperForm.years.length > 0 ? paperForm.years : [],
          dtags: typeConfigs[type].tags
        };
        dQuestionList.push(duoDTO);

        // 只有勾选了使用明细配置并且该题型已进行明细配置时才添加
        if (paperForm.useDetailConfig && hasDetailConfigured[type] && questions[type] && questions[type].length > 0) {
          // 遍历该题型的所有明细配置
          questions[type].forEach(item => {
            const detailDTO = {
              equestionType: questionTypeCode,
              edifficulty: item.difficulty,
              escore: item.score.toString(),
              eknowledgePoints: item.knowledgePoints.map(String),
              echapters: item.chapters.map(String),
              eregion: item.region,
              eyear: item.year,
              egradeId: item.gradeId,
              epaperType: item.paperType,
              erequiredNumber: 1,
              etags: item.tags
            };
            eQuestionList.push(detailDTO);
          });
        }
      }
    }

    // 转换为后端DTO格式
    const questionDuoDTOList = dQuestionList.map(duo => ({
      questionType: duo.dquestionType,
      requiredNumber: duo.drequiredNumber,
      difficulty: duo.ddifficulty,
      score: duo.dscore,
      knowledgePoints: duo.dknowledgePoints,
      chapters: duo.dchapters,
      region: duo.dregion,
      gradeIds: duo.dgradeIds,
      paperTypes: duo.dpaperTypes,
      years: duo.dyears,
      tags: duo.dtags
    }));

    // 只有勾选了使用明细配置时才使用questionDTOList
    const questionDTOList = paperForm.useDetailConfig ? eQuestionList.map(detail => ({
      questionType: detail.equestionType,
      difficulty: detail.edifficulty,
      score: detail.escore,
      knowledgePoints: detail.eknowledgePoints,
      chapters: detail.echapters,
      region: detail.eregion,
      year: detail.eyear,
      gradeId: detail.egradeId,
      paperType: detail.epaperType,
      requiredNumber: detail.erequiredNumber,
      tags: detail.etags
    })) : [];

    // 符合QhGeneratePaperDTO格式的参数
    const paperData = {
      // 试卷基本信息 - c前缀已与后端一致
      cpaperName: drawerForm.paperName,
      cpaperType: drawerForm.paperType,
      cregion: drawerForm.region,
      cyear: drawerForm.year,

      // 其他参数
      mulu: drawerForm.folderId, // 使用组卷目录ID
      libraryIds: paperForm.libraryIds,

      // 使用转换后的符合后端格式的数据
      questionDuoDTOList: questionDuoDTOList,
      questionDTOList: questionDTOList
    };

    // 调用API生成试卷
    const loading = ElLoading.service({
      lock: true,
      text: '正在组卷中，请稍候...',
      background: 'rgba(0, 0, 0, 0.7)'
    });

    request({
      url: '/qh/paper/test',
      method: 'post',
      data: paperData
    }).then(response => {
      loading.close();
      if (processTestData(response)) {
        drawerVisible.value = false;
      }
    }).catch(error => {
      loading.close();
    });
  }

  // 将后端返回的数据转换为树状结构
  const convertToTreeData = (data) => {
    if (!data || !Array.isArray(data)) return []

    // 检查数据是否已经是树结构（有children属性）
    const isTreeStructure = data.some(item => item.children && Array.isArray(item.children))

    if (isTreeStructure) {
      // 数据已经是树结构，只需确保每个节点都有正确的label和children属性
      const processTree = (items) => {
        return items.map(item => {
          // 复制节点，确保不直接修改原始数据
          const node = {
            ...item,
            label: item.label || item.name, // 优先使用label，如果没有则使用name
            id: item.id
          }

          // 确保每个节点都有children属性
          if (!node.children) {
            node.children = []
          } else if (Array.isArray(node.children) && node.children.length > 0) {
            // 递归处理子节点
            node.children = processTree(node.children)
          }

          return node
        })
      }

      return processTree(data)
    } else {
      // 数据是扁平结构，需要根据parentId构建树结构
      const result = []
      const map = {}

      // 第一遍：创建所有节点的映射
      data.forEach(item => {
        map[item.id] = {
          id: item.id,
          label: item.label || item.name,
          children: []
        }
      })

      // 第二遍：构建树结构
      data.forEach(item => {
        const node = map[item.id]

        // 如果有父节点且不是顶级节点
        if (item.parentId && item.parentId !== '0' && map[item.parentId]) {
          // 将当前节点添加到父节点的children中
          map[item.parentId].children.push(node)
        } else {
          // 顶级节点直接添加到结果数组
          result.push(node)
        }
      })

      return result
    }
  }

  // 获取题库数据
  const fetchLibraryOptions = () => {
    request({
      url: '/qh/knowledgeTree/nodeType',
      method: 'post',
      data: {
        nodeType: 1,
        ancestors: ''
      }
    }).then(response => {
      if (response.code === 200 && Array.isArray(response.data)) {
        libraryOptions.value = response.data.map(item => ({
          value: item.id,
          label: item.label || item.name // 使用label或name作为显示标签
        }))
      } else {
        ElMessage.error(response.msg || '获取题库数据失败')
      }
    })
  }

  // 获取年级数据
  const fetchGradeOptions = () => {
    request({
      url: '/qh/knowledgeTree/nodeType',
      method: 'post',
      data: {
        nodeType: 2,
        ancestors: ''
      }
    }).then(response => {
      if (response.code === 200 && Array.isArray(response.data)) {
        gradeOptions.value = response.data.map(item => ({
          value: item.id,
          label: item.label || item.name // 使用label或name作为显示标签
        }))
      } else {
        ElMessage.error(response.msg || '获取年级数据失败')
      }
    })
  }

  // 获取章节数据
  const fetchChapterData = () => {
    const requestData = {
      nodeType: 4,
      ancestors: ''
    }
    
    // 只有当 libraryIds 有值时才添加 ancestorIdList 和 ancestors
    if (paperForm.libraryIds && paperForm.libraryIds.length > 0) {
      requestData.ancestorIdList = paperForm.libraryIds
      requestData.ancestors = paperForm.libraryIds.join(',')
    }
    
    request({
      url: '/qh/knowledgeTree/nodeType',
      method: 'post',
      data: requestData
    }).then(response => {
      if (response.code === 200 && Array.isArray(response.data)) {
        chapterTreeData.value = convertToTreeData(response.data)
      } else {
        ElMessage.error(response.msg || '获取章节数据失败')
      }
    })
  }

  // 获取知识点数据
  const fetchKnowledgeData = () => {
    const requestData = {
      nodeType: 5,
      ancestors: ''
    }
    
    // 只有当 selectedChapters 有值时才添加 ancestorIdList 和 ancestors
    if (selectedChapters.value && selectedChapters.value.length > 0) {
      requestData.ancestorIdList = selectedChapters.value
      requestData.ancestors = selectedChapters.value.join(',')
    }
    
    request({
      url: '/qh/knowledgeTree/nodeType',
      method: 'post',
      data: requestData
    }).then(response => {
      if (response.code === 200 && Array.isArray(response.data)) {
        knowledgeTreeData.value = convertToTreeData(response.data)
      } else {
        ElMessage.error(response.msg || '获取知识点数据失败')
      }
    })
  }

  // 题型表格数据
  const questionTypeTableData = computed(() => {
    return Object.keys(questionCounts).map(type => {
      const typeCode = questionTypeMapping[type];
      const typeItem = sys_qh_questions_type.value.find(item => item.value === typeCode);
      const typeName = typeItem ? typeItem.label : questionTypeLabels[type];

      return {
        type,
        typeName,
        typeCode,
        count: questionCounts[type],
        score: typeConfigs[type].score
      };
    });
  });

  // 年份选择相关
  let yearPickerVisible = ref(false)
  const tempYear = ref('')

  // 显示年份选择器
  const showYearPicker = () => {
    tempYear.value = ''
    yearPickerVisible.value = true
  }

  // 添加选择的年份
  const addSelectedYear = () => {
    if (tempYear.value && !paperForm.years.includes(tempYear.value)) {
      paperForm.years.push(tempYear.value)
    }
    yearPickerVisible.value = false
  }

  // 移除年份
  const removeYear = (year) => {
    const index = paperForm.years.indexOf(year)
    if (index !== -1) {
      paperForm.years.splice(index, 1)
    }
  }

  // 年级选择相关方法
  const toggleGradeSelection = (value) => {
    const index = paperForm.gradeIds.indexOf(value)
    if (index === -1) {
      // 如果不在数组中，则添加
      paperForm.gradeIds.push(value)
    } else {
      // 如果已在数组中，则移除
      paperForm.gradeIds.splice(index, 1)
    }
  }

  const removeGradeSelection = (value) => {
    const index = paperForm.gradeIds.indexOf(value)
    if (index !== -1) {
      paperForm.gradeIds.splice(index, 1)
    }
  }

  const clearGradeSelection = () => {
    paperForm.gradeIds = []
  }

  // 类型选择相关方法
  const toggleTypeSelection = (value) => {
    const index = paperForm.paperTypes.indexOf(value)
    if (index === -1) {
      // 如果不在数组中，则添加
      paperForm.paperTypes.push(value)
    } else {
      // 如果已在数组中，则移除
      paperForm.paperTypes.splice(index, 1)
    }
  }

  const removeTypeSelection = (value) => {
    const index = paperForm.paperTypes.indexOf(value)
    if (index !== -1) {
      paperForm.paperTypes.splice(index, 1)
    }
  }

  const clearTypeSelection = () => {
    paperForm.paperTypes = []
  }

  // 页面加载时进行初始化
  onMounted(() => {
    // 获取数据
    fetchLibraryOptions()
    fetchGradeOptions()
    // 初始化难度值
    initDifficultyValues()
    
    // 检查regionData是否正确加载
    console.log('regionData加载状态:', regionData ? '已加载' : '未加载', regionData)
    
    // 添加点击外部关闭树状选择框的事件监听
    document.addEventListener('click', handleClickOutside)
  })

  // 添加地区选择相关变量和方法
  let regionInputVisible = ref(false)
  const tempRegion = ref([])

  // 显示地区输入框
  const showRegionInput = () => {
    tempRegion.value = []
    regionInputVisible.value = true
  }

  // 添加选择的地区
  const addSelectedRegion = () => {
    if (tempRegion.value && tempRegion.value.length > 0) {
      // 格式化地区名称，使用codeToText将代码转换为文本
      const regionNames = tempRegion.value.map(code => codeToText[code] || code);
      const formattedRegion = regionNames.join(' / ');
      
      if (!paperForm.regions.includes(formattedRegion)) {
        paperForm.regions.push(formattedRegion);
        // 兼容原有单一地区字段，将多个地区用逗号连接
        paperForm.region = paperForm.regions.join(',');
      }
    }
    regionInputVisible.value = false;
  }

  // 移除地区
  const removeRegion = (region) => {
    const index = paperForm.regions.indexOf(region)
    if (index !== -1) {
      paperForm.regions.splice(index, 1)
      // 更新原有单一地区字段
      paperForm.region = paperForm.regions.join(',')
    }
  }

  // 组件销毁前移除事件监听器
  onBeforeUnmount(() => {
    document.removeEventListener('click', handleClickOutside)
  })
</script>

<style lang="scss" scoped>
  .app-container {
    padding: 20px;
  }

  .page-container {
    display: flex;
    gap: 20px;
    height: 100%;
  }

  /* 左侧面板样式 */
  .left-panel {
    width: 300px;
    flex-shrink: 0;
  }

  .left-panel-section {
    background-color: #fff;
    border-radius: 4px;
    padding: 15px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    height: calc(100vh - 120px);
    display: flex;
    flex-direction: column;
  }

  .section-title {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    font-weight: bold;
    font-size: 16px;
  }

  .small-title {
    font-size: 14px;
    margin-bottom: 10px;
  }

  .title-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background-color: #409EFF;
    color: white;
    border-radius: 50%;
    margin-right: 8px;
    font-size: 14px;
  }

  .title-text {
    font-size: 16px;
  }

  .subject-select-container {
    margin-bottom: 15px;
  }

  .subject-select-container .el-select {
    width: 100%;
  }

  .full-width-select {
    width: 100%;
  }

  .knowledge-chapter-container {
    display: flex;
    flex-direction: column;
    flex: 1;
    gap: 10px;
    overflow: hidden;
    min-height: 0;
  }

  .knowledge-section, .chapter-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    overflow: hidden;
  }

  .knowledge-tree-container, .chapter-tree-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    min-height: 0;
  }

  .knowledge-tree-container .el-input, .chapter-tree-container .el-input {
    margin-bottom: 10px;
  }

  .selected-tags {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding: 3px 8px;
    background-color: #f5f7fa;
    border-radius: 4px;
    font-size: 12px;
  }

  .selected-count {
    font-size: 12px;
    color: #606266;
  }

  .tag-item {
    margin-bottom: 5px;
  }

  .section-subtitle {
    font-weight: bold;
    margin-bottom: 10px;
    color: #606266;
  }

  /* 右侧面板样式 */
  .right-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
    overflow: auto;
  }

  .right-panel-section {
    background-color: #fff;
    border-radius: 4px;
    padding: 15px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }

  /* 第二个right-panel-section (题型配置区域) 铺满屏幕 */
  .right-panel-section:nth-child(2) {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: calc(100vh - 480px);
  }

  /* 组卷设置区域样式 */
  .grade-section,
  .difficulty-section,
  .region-section,
  .type-section,
  .year-section,
  .other-section {
    margin-bottom: 15px;
    display: flex;
    align-items: flex-start;
  }

  .grade-label,
  .difficulty-label,
  .region-label,
  .type-label,
  .year-label,
  .other-label {
    width: 60px;
    flex-shrink: 0;
    color: #606266;
    padding-top: 5px;
    font-size: 13px;
  }

  .grade-options,
  .difficulty-picker,
  .region-selects,
  .type-options,
  .year-options,
  .other-options {
    flex: 1;
  }

  .medium-width-select {
    width: 260px;
  }

  .difficulty-distribution {
    margin-top: 10px;
  }

  .difficulty-bar {
    display: flex;
    height: 20px;
    border-radius: 4px;
    overflow: hidden;
  }

  .bar-segment {
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
  }

  .star-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 5px;
    font-size: 12px;
    color: #909399;
  }

  .region-selects {
    display: flex;
    gap: 10px;
    align-items: center;
  }

  .region-selects .el-select {
    width: 120px;
  }

  .type-options,
  .year-options {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }

  .filter-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
  }

  .blank-space {
    flex: 1;
  }

  /* 题型配置样式 - 新的行样式 */
  .question-types-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 20px;
    max-height: calc(100vh - 400px);
    overflow-y: auto;
  }

  .question-type-item {
    border: 1px solid #ebeef5;
    border-radius: 4px;
    padding: 10px;
  }

  .question-type-row {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: center;
  }

  .question-type-label {
    font-weight: bold;
    min-width: 80px;
  }

  .question-count-container,
  .question-point-container,
  .question-detail-container {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .question-count-label,
  .detail-label {
    font-size: 13px;
    color: #606266;
  }

  .question-count-selector {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .count-unit, .score-unit {
    margin-left: 5px;
    color: #606266;
    font-size: 12px;
  }

  .submit-button {
    display: flex;
    justify-content: space-between;
  }

  /* 树形控件最大高度 */
  .el-tree {
    flex: 1;
    overflow-y: auto;
    font-size: 13px;
    min-height: 0;
  }

  /* 调整滚动条样式 */
  .el-tree::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .el-tree::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background-color: #c0c4cc;
  }

  .el-tree::-webkit-scrollbar-track {
    border-radius: 3px;
    background-color: #ebeef5;
  }

  /* 空容器样式 */
  .empty-selected {
    padding: 20px;
    text-align: center;
    color: #909399;
    font-size: 14px;
  }

  /* 题型配置表格头部样式 */
  .question-type-header {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    border-radius: 4px;
    color: #606266;
  }

  .question-type-label-header {
    min-width: 80px;
    flex: 1;
  }

  .question-count-header {
    width: 150px;
    text-align: center;
  }

  .question-point-header {
    width: 150px;
    text-align: center;
  }

  .question-detail-header {
    width: 100px;
    text-align: center;
  }

  /* 题型行样式优化 */
  .question-type-row {
    display: flex;
    align-items: center;
  }

  .question-type-label {
    font-weight: bold;
    min-width: 80px;
    flex: 1;
  }

  .question-count-container {
    width: 150px;
    display: flex;
    justify-content: center;
  }

  .question-point-container {
    width: 150px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .question-detail-container {
    width: 100px;
    display: flex;
    justify-content: center;
  }

  .disabled-points {
    color: #999;
    font-style: italic;
  }

  .points-toggle {
    margin: 0;
  }

  /* 已选知识点和章节标签样式优化 */
  .selected-tags {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding: 5px 10px;
    background-color: #ecf5ff;
    border-radius: 4px;
    font-size: 13px;
    border-left: 3px solid #409EFF;
  }

  /* 题型表格样式 */
  .question-types-table {
    margin-bottom: 20px;
    flex: 1;
    overflow: auto;
  }

  .points-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
  }

  .question-count-selector {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .count-unit, .score-unit {
    margin-left: 5px;
    color: #606266;
    font-size: 12px;
  }

  /* 新增加的样式 */
  .question-count {
    display: inline-block;
    min-width: 40px;
    text-align: center;
    margin: 8px 0;
    font-size: 14px;
  }

  .score-input {
    width: 80px;
  }

  .count-input {
    width: 120px;
  }

  .el-button.is-circle {
    padding: 6px;
  }

  .question-count-selector {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }

  .score-input-container {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .score-input-container .score-unit {
    margin-left: 8px;
  }

  /* 自定义数字输入框样式 */
  .number-input-wrapper {
    display: flex;
    align-items: center;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    overflow: hidden;
  }

  .number-input-wrapper .number-input {
    width: 50px;
    border: none;
  }

  .number-input-wrapper .number-input .el-input__inner {
    border: none;
    text-align: center;
    padding: 0;
  }

  .number-control {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background-color: #f5f7fa;
    cursor: pointer;
    user-select: none;
    font-size: 16px;
    font-weight: bold;
  }

  .number-control:hover {
    background-color: #e4e7ed;
  }

  .number-control.disabled {
    color: #c0c4cc;
    cursor: not-allowed;
    background-color: #f5f7fa;
  }

  /* 去除el-input数字类型的默认箭头 */
  .number-input .el-input__inner::-webkit-inner-spin-button,
  .number-input .el-input__inner::-webkit-outer-spin-button,
  .score-input::-webkit-inner-spin-button,
  .score-input::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  .year-tags-container {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    margin-top: 5px;
    padding-left: 2px;
  }

  .year-tag {
    margin-right: 5px;
    margin-bottom: 8px;
  }

  .year-picker-container {
    padding: 10px 0;
  }

  .options-container {
    display: flex;
    flex-wrap: wrap;
    margin-top: 0px;
  }

  .option-item {
    display: flex;
    align-items: center;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
    position: relative;
    font-size: 14px;
  }

  .option-item.no-border {
    border: none;
  }

  .option-item:hover {
    color: #409EFF;
  }

  .option-selected {
    background-color: #ecf5ff;
    border: 1px solid #409EFF;
    color: #409EFF;
    margin: 0 2px;
  }

  .expand-control {
    display: none;
  }

  .year-section {
    margin-bottom: 15px;
  }

  .region-tags-container {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    margin-top: 5px;
    padding-left: 2px;
  }

  .region-tag {
    margin-right: 5px;
    margin-bottom: 8px;
  }

  .region-input-container {
    padding: 10px 0;
  }
</style>